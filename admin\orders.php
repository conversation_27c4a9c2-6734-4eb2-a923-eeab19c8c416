<?php
require_once '../config/database.php';

$page_title = 'จัดการคำสั่งซื้อ';
$breadcrumb = [
    ['title' => 'หน้าหลัก', 'url' => 'dashboard.php'],
    ['title' => 'จัดการคำสั่งซื้อ']
];

// $pdo ถูกสร้างแล้วใน database.php
$message = '';
$error = '';

// จัดการการอัพเดตสถานะ
if ($_POST && isset($_POST['action'])) {
    $action = $_POST['action'];

    try {
        if ($action === 'update_status') {
            $order_id = (int)$_POST['order_id'];
            $status = $_POST['status'];

            $stmt = $pdo->prepare("UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$status, $order_id]);

            $message = 'อัพเดตสถานะคำสั่งซื้อเรียบร้อยแล้ว';
        }

        if ($action === 'add_note') {
            $order_id = (int)$_POST['order_id'];
            $note = trim($_POST['note']);

            $stmt = $pdo->prepare("UPDATE orders SET admin_note = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$note, $order_id]);

            $message = 'เพิ่มหมายเหตุเรียบร้อยแล้ว';
        }

    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// ตัวแปรสำหรับการค้นหาและกรอง
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

// สร้างคำสั่ง SQL สำหรับค้นหา
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(o.order_number LIKE ? OR c.name LIKE ? OR c.email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($status_filter)) {
    $where_conditions[] = "o.status = ?";
    $params[] = $status_filter;
}

if (!empty($date_from)) {
    $where_conditions[] = "DATE(o.created_at) >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(o.created_at) <= ?";
    $params[] = $date_to;
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// นับจำนวนคำสั่งซื้อทั้งหมด
$count_sql = "SELECT COUNT(*) as total FROM orders o LEFT JOIN customers c ON o.customer_id = c.id $where_clause";
$count_stmt = $pdo->prepare($count_sql);
$count_stmt->execute($params);
$total_orders = $count_stmt->fetch()['total'];
$total_pages = ceil($total_orders / $limit);

// ดึงข้อมูลคำสั่งซื้อ
$sql = "
    SELECT o.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone
    FROM orders o
    LEFT JOIN customers c ON o.customer_id = c.id
    $where_clause
    ORDER BY o.created_at DESC
    LIMIT $limit OFFSET $offset
";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$orders = $stmt->fetchAll();

// สถิติการสั่งซื้อ
try {
    $stats_sql = "
        SELECT
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
            COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_orders,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
            COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders,
            SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as total_revenue,
            COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_orders
        FROM orders
    ";
    $stats_stmt = $pdo->query($stats_sql);
    $stats = $stats_stmt->fetch();
} catch (Exception $e) {
    $stats = [
        'total_orders' => 0,
        'pending_orders' => 0,
        'processing_orders' => 0,
        'completed_orders' => 0,
        'cancelled_orders' => 0,
        'total_revenue' => 0,
        'today_orders' => 0
    ];
}

include 'includes/header.php';
?>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">จัดการคำสั่งซื้อ</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="dashboard.php">หน้าหลัก</a></li>
                    <li class="breadcrumb-item active">จัดการคำสั่งซื้อ</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">

        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <h5><i class="icon fas fa-check"></i> สำเร็จ!</h5>
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <h5><i class="icon fas fa-ban"></i> ข้อผิดพลาด!</h5>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <!-- Small boxes (Stat box) -->
        <div class="row">
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3><?= number_format($stats['total_orders']) ?></h3>
                        <p>คำสั่งซื้อทั้งหมด</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <a href="#" class="small-box-footer">ดูรายละเอียด <i class="fas fa-arrow-circle-right"></i></a>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3><?= number_format($stats['pending_orders']) ?></h3>
                        <p>รอดำเนินการ</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <a href="?status=pending" class="small-box-footer">ดูรายการ <i class="fas fa-arrow-circle-right"></i></a>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3><?= number_format($stats['completed_orders']) ?></h3>
                        <p>เสร็จสิ้น</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-check"></i>
                    </div>
                    <a href="?status=completed" class="small-box-footer">ดูรายการ <i class="fas fa-arrow-circle-right"></i></a>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3>฿<?= number_format($stats['total_revenue']) ?></h3>
                        <p>รายได้รวม</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <a href="reports.php" class="small-box-footer">ดูรายงาน <i class="fas fa-arrow-circle-right"></i></a>
                </div>
            </div>
        </div>

        <!-- ฟอร์มค้นหา -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">ค้นหาและกรองข้อมูล</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#exportModal">
                        <i class="fas fa-download mr-1"></i> ส่งออกข้อมูล
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>ค้นหา</label>
                            <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>"
                                   placeholder="เลขที่คำสั่งซื้อ, ชื่อลูกค้า, อีเมล">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>สถานะ</label>
                            <select class="form-control" name="status">
                                <option value="">ทั้งหมด</option>
                                <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>รอดำเนินการ</option>
                                <option value="processing" <?= $status_filter === 'processing' ? 'selected' : '' ?>>กำลังดำเนินการ</option>
                                <option value="completed" <?= $status_filter === 'completed' ? 'selected' : '' ?>>เสร็จสิ้น</option>
                                <option value="cancelled" <?= $status_filter === 'cancelled' ? 'selected' : '' ?>>ยกเลิก</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>วันที่เริ่มต้น</label>
                            <input type="date" class="form-control" name="date_from" value="<?= htmlspecialchars($date_from) ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>วันที่สิ้นสุด</label>
                            <input type="date" class="form-control" name="date_to" value="<?= htmlspecialchars($date_to) ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="btn-group btn-block">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search mr-1"></i> ค้นหา
                                </button>
                                <a href="orders.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-undo mr-1"></i> รีเซ็ต
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- ตารางคำสั่งซื้อ -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">รายการคำสั่งซื้อ</h3>
                <div class="card-tools">
                    <span class="badge badge-primary">พบ <?= count($orders) ?> รายการ</span>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>เลขที่คำสั่งซื้อ</th>
                                <th>ลูกค้า</th>
                                <th>จำนวนเงิน</th>
                                <th>สถานะ</th>
                                <th>วันที่สั่งซื้อ</th>
                                <th>การดำเนินการ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($orders)): ?>
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">ไม่พบคำสั่งซื้อ</p>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($orders as $order): ?>
                                <tr>
                                    <td>
                                        <strong>#<?= htmlspecialchars($order['order_number']) ?></strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?= htmlspecialchars($order['customer_name'] ?: 'ไม่ระบุ') ?></strong><br>
                                            <small class="text-muted"><?= htmlspecialchars($order['customer_email'] ?: '') ?></small><br>
                                            <small class="text-muted"><?= htmlspecialchars($order['customer_phone'] ?: '') ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>฿<?= number_format($order['total_amount']) ?></strong>
                                    </td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        $status_text = '';
                                        switch ($order['status']) {
                                            case 'pending':
                                                $status_class = 'badge-warning';
                                                $status_text = 'รอดำเนินการ';
                                                break;
                                            case 'processing':
                                                $status_class = 'badge-info';
                                                $status_text = 'กำลังดำเนินการ';
                                                break;
                                            case 'completed':
                                                $status_class = 'badge-success';
                                                $status_text = 'เสร็จสิ้น';
                                                break;
                                            case 'cancelled':
                                                $status_class = 'badge-danger';
                                                $status_text = 'ยกเลิก';
                                                break;
                                        }
                                        ?>
                                        <span class="badge <?= $status_class ?>"><?= $status_text ?></span>
                                    </td>
                                    <td>
                                        <?= date('d/m/Y H:i', strtotime($order['created_at'])) ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="viewOrder(<?= $order['id'] ?>)" title="ดูรายละเอียด">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="updateStatus(<?= $order['id'] ?>, '<?= $order['status'] ?>')" title="เปลี่ยนสถานะ">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="addNote(<?= $order['id'] ?>)" title="เพิ่มหมายเหตุ">
                                                <i class="fas fa-sticky-note"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="d-flex justify-content-center mt-4">
            <nav aria-label="Page navigation">
                <ul class="pagination">
                    <?php
                    $query_params = $_GET;
                    unset($query_params['page']);
                    $query_string = http_build_query($query_params);
                    ?>

                    <li class="page-item <?= $page <= 1 ? 'disabled' : '' ?>">
                        <a class="page-link" href="?page=<?= $page - 1 ?>&<?= $query_string ?>">ก่อนหน้า</a>
                    </li>

                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                            <a class="page-link" href="?page=<?= $i ?>&<?= $query_string ?>"><?= $i ?></a>
                        </li>
                    <?php endfor; ?>

                    <li class="page-item <?= $page >= $total_pages ? 'disabled' : '' ?>">
                        <a class="page-link" href="?page=<?= $page + 1 ?>&<?= $query_string ?>">ถัดไป</a>
                    </li>
                </ul>
            </nav>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Modal สำหรับอัพเดตสถานะ -->
<div class="modal fade" id="statusModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">อัพเดตสถานะคำสั่งซื้อ</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="order_id" id="status_order_id">

                    <div class="form-group">
                        <label>สถานะใหม่</label>
                        <select class="form-control" name="status" id="new_status" required>
                            <option value="pending">รอดำเนินการ</option>
                            <option value="processing">กำลังดำเนินการ</option>
                            <option value="completed">เสร็จสิ้น</option>
                            <option value="cancelled">ยกเลิก</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer justify-content-between">
                    <button type="button" class="btn btn-default" data-dismiss="modal">ยกเลิก</button>
                    <button type="submit" class="btn btn-primary">บันทึก</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal สำหรับเพิ่มหมายเหตุ -->
<div class="modal fade" id="noteModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">เพิ่มหมายเหตุ</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_note">
                    <input type="hidden" name="order_id" id="note_order_id">

                    <div class="form-group">
                        <label>หมายเหตุ</label>
                        <textarea class="form-control" name="note" rows="4" placeholder="ใส่หมายเหตุสำหรับคำสั่งซื้อนี้..."></textarea>
                    </div>
                </div>
                <div class="modal-footer justify-content-between">
                    <button type="button" class="btn btn-default" data-dismiss="modal">ยกเลิก</button>
                    <button type="submit" class="btn btn-primary">บันทึก</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal สำหรับดูรายละเอียดคำสั่งซื้อ -->
<div class="modal fade" id="orderDetailModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">รายละเอียดคำสั่งซื้อ</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="orderDetailContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Modal สำหรับส่งออกข้อมูล -->
<div class="modal fade" id="exportModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">ส่งออกข้อมูลคำสั่งซื้อ</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="POST" action="export_orders.php">
                <div class="modal-body">
                    <div class="form-group">
                        <label>รูปแบบไฟล์</label>
                        <select class="form-control" name="format" required>
                            <option value="excel">Excel (.xlsx)</option>
                            <option value="csv">CSV (.csv)</option>
                            <option value="pdf">PDF (.pdf)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>ช่วงวันที่</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="date" class="form-control" name="export_date_from" placeholder="วันที่เริ่มต้น">
                            </div>
                            <div class="col-6">
                                <input type="date" class="form-control" name="export_date_to" placeholder="วันที่สิ้นสุด">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer justify-content-between">
                    <button type="button" class="btn btn-default" data-dismiss="modal">ยกเลิก</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-download mr-1"></i>ส่งออก
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Functions for modals
    window.updateStatus = function(orderId, currentStatus) {
        $('#status_order_id').val(orderId);
        $('#new_status').val(currentStatus);
        $('#statusModal').modal('show');
    };

    window.addNote = function(orderId) {
        $('#note_order_id').val(orderId);
        $('#noteModal').modal('show');
    };

    window.viewOrder = function(orderId) {
        // Load order details via AJAX
        $.get(`order_detail.php?id=${orderId}`)
            .done(function(html) {
                $('#orderDetailContent').html(html);
                $('#orderDetailModal').modal('show');
            })
            .fail(function() {
                alert('เกิดข้อผิดพลาดในการโหลดข้อมูล');
            });
    };
});
</script>

<?php include 'includes/footer.php'; ?>
