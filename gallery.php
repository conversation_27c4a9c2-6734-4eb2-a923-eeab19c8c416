<?php
session_start();

// ดึงข้อมูล gallery จากฐานข้อมูล
$gallery_items = [];
try {
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';
        
        if (isset($pdo)) {
            $stmt = $pdo->query("SELECT * FROM gallery_images WHERE is_active = 1 ORDER BY sort_order, created_at DESC");
            $gallery_items = $stmt->fetchAll();
        }
    }
} catch (Exception $e) {
    error_log("Database error in gallery.php: " . $e->getMessage());
}

// Fallback data ถ้าไม่มีข้อมูลจากฐานข้อมูล
if (empty($gallery_items)) {
    $gallery_items = [
        ['id' => 1, 'title' => 'ผลงานการออกแบบ 1', 'image_path' => 'https://img2.pic.in.th/pic/LINE_ALBUM__241018_1.jpg'],
        ['id' => 2, 'title' => 'ผลงานการออกแบบ 2', 'image_path' => 'https://img5.pic.in.th/file/secure-sv1/LINE_ALBUM__241018_2.jpg'],
        ['id' => 3, 'title' => 'ผลงานการออกแบบ 3', 'image_path' => 'https://img5.pic.in.th/file/secure-sv1/LINE_ALBUM__241018_3755795ed32b03ea7.jpg'],
        ['id' => 4, 'title' => 'ผลงานการออกแบบ 4', 'image_path' => 'https://img5.pic.in.th/file/secure-sv1/LINE_ALBUM__241018_5.jpg'],
        ['id' => 5, 'title' => 'ผลงานการออกแบบ 5', 'image_path' => 'https://img5.pic.in.th/file/secure-sv1/644002183119016645_n.jpg'],
        ['id' => 6, 'title' => 'ผลงานการออกแบบ 6', 'image_path' => 'https://img2.pic.in.th/pic/LINE_ALBUM__241018_7.jpg'],
        ['id' => 7, 'title' => 'ผลงานการออกแบบ 7', 'image_path' => 'https://img2.pic.in.th/pic/LINE_ALBUM__241018_8.jpg'],
        ['id' => 8, 'title' => 'ผลงานการออกแบบ 8', 'image_path' => 'https://img2.pic.in.th/pic/LINE_ALBUM__241018_10.jpg']
    ];
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>ผลงานของเรา - GT Sport Design</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .gallery-hero {
            background: linear-gradient(135deg, #ee501b, #ff6b35);
            color: white;
            padding: 100px 0 60px;
            text-align: center;
        }
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        .gallery-item {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            height: 300px;
            background: #f5f5f5;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        .gallery-item:hover {
            transform: translateY(-10px);
        }
        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .gallery-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            color: white;
            padding: 20px;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }
        .gallery-item:hover .gallery-overlay {
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <!-- Hero Section -->
    <section class="gallery-hero">
        <div class="container">
            <h1 class="display-4 fw-bold mb-3">ผลงานของเรา</h1>
            <p class="lead">ชมผลงานการออกแบบและผลิตเสื้อกีฬาคุณภาพสูงของเรา</p>
        </div>
    </section>

    <!-- Gallery Section -->
    <section class="py-5">
        <div class="container">
            <div class="gallery-grid">
                <?php foreach ($gallery_items as $item): ?>
                    <div class="gallery-item" onclick="openModal('<?= htmlspecialchars($item['image_path']) ?>', '<?= htmlspecialchars($item['title']) ?>')">
                        <img src="<?= htmlspecialchars($item['image_path']) ?>" 
                             alt="<?= htmlspecialchars($item['title']) ?>" 
                             loading="lazy">
                        <div class="gallery-overlay">
                            <h5><?= htmlspecialchars($item['title']) ?></h5>
                            <?php if (!empty($item['description'])): ?>
                                <p class="mb-0"><?= htmlspecialchars($item['description']) ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Modal for Image Preview -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">ผลงานของเรา</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="" class="img-fluid">
                </div>
            </div>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function openModal(imageSrc, title) {
            document.getElementById('modalImage').src = imageSrc;
            document.getElementById('modalTitle').textContent = title;
            new bootstrap.Modal(document.getElementById('imageModal')).show();
        }
    </script>
</body>
</html>
