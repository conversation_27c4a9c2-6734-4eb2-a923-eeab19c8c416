<?php
session_start();
require_once 'config/database.php';
$pdo = getDbConnection();

// การกรองและการค้นหา
$category_id = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$search = isset($_GET['search']) ? $_GET['search'] : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 12;
$offset = ($page - 1) * $per_page;

// สร้าง query ฐาน
$query = "SELECT p.*, c.name as category_name FROM products p 
          LEFT JOIN categories c ON p.category_id = c.id 
          WHERE p.status = 'active'";
$params = [];

// เ่่มเงื่อนไขการกรอง
if ($category_id > 0) {
    $query .= " AND p.category_id = ?";
    $params[] = $category_id;
}

if (!empty($search)) {
    $query .= " AND (p.name LIKE ? OR p.description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

// Query จำนวน รายการ
$count_query = str_replace("p.*, c.name as category_name", "COUNT(*) as total", $query);
$stmt = $pdo->prepare($count_query);
$stmt->execute($params);
$total_records = $stmt->fetch()['total'];
$total_pages = ceil($total_records / $per_page);

// Query ข้อมูล
$query .= " ORDER BY p.created_at DESC LIMIT $offset, $per_page";
$stmt = $pdo->prepare($query);
$stmt->execute($params);
$products = $stmt->fetchAll();

// ข้อมูลหมวด
$stmt = $pdo->query("SELECT * FROM categories WHERE status = 'active' ORDER BY name");
$categories = $stmt->fetchAll();

include 'includes/header.php';
?>

<!-- Products Section -->
<section class="products-section">
    <div class="container">
        <h1 class="page-title">ค้า - GT Sport Design</h1>
        
        <!-- Filter and Search -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-6">
                    <form action="" method="get" class="search-form">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" placeholder="ค้นหาค้า..." value="<?php echo htmlspecialchars($search); ?>">
                            <button type="submit" class="btn btn-primary"><i class="fas fa-search"></i></button>
                        </div>
                    </form>
                </div>
                <div class="col-md-6">
                    <div class="category-filter">
                        <select class="form-select" id="categoryFilter" onchange="filterByCategory(this.value)">
                            <option value="0">หมวด</option>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>" <?php echo ($category_id == $category['id']) ? 'selected' : ''; ?>>
                                <?php echo $category['name']; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Products Grid -->
        <div class="row products-grid">
            <?php if (count($products) > 0): ?>
                <?php foreach ($products as $product): ?>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="product-card">
                        <div class="product-image">
                            <img src="uploads/products/<?php echo $product['image']; ?>" alt="<?php echo $product['name']; ?>">
                            <?php if ($product['is_featured']): ?>
                            <span class="badge bg-primary">แนะนำ</span>
                            <?php endif; ?>
                        </div>
                        <div class="product-info">
                            <h3><?php echo $product['name']; ?></h3>
                            <p class="category"><?php echo $product['category_name']; ?></p>
                            <p class="price"><?php echo number_format($product['price']); ?> บาท</p>
                            <a href="product-detail.php?id=<?php echo $product['id']; ?>" class="btn btn-sm btn-primary">ดูรายละเอียด</a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12 text-center py-5">
                    <p>ไม่พบค้าที่ค้นหา</p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page-1; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo $category_id > 0 ? '&category='.$category_id : ''; ?>" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                <?php endif; ?>
                
                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo $category_id > 0 ? '&category='.$category_id : ''; ?>">
                        <?php echo $i; ?>
                    </a>
                </li>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page+1; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo $category_id > 0 ? '&category='.$category_id : ''; ?>" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
    </div>
</section>

<script>
function filterByCategory(categoryId) {
    let url = new URL(window.location.href);
    url.searchParams.set('category', categoryId);
    url.searchParams.delete('page');
    window.location.href = url.toString();
}
</script>

<?php include 'includes/footer.php'; ?>


