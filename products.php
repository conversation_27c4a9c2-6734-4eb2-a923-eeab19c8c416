<?php
session_start();

// ดึงข้อมูลสินค้าและหมวดหมู่จากฐานข้อมูล
$products = [];
$categories = [];
$selected_category = $_GET['category'] ?? '';
$search = $_GET['search'] ?? '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 12;
$offset = ($page - 1) * $per_page;

try {
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';

        if (isset($pdo)) {
            // ดึงหมวดหมู่
            $stmt = $pdo->query("SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order");
            $categories = $stmt->fetchAll();

            // สร้าง query สำหรับสินค้า
            $where_conditions = ["p.is_active = 1"];
            $params = [];

            if ($selected_category) {
                $where_conditions[] = "c.slug = :category";
                $params[':category'] = $selected_category;
            }

            if ($search) {
                $where_conditions[] = "(p.name LIKE :search OR p.description LIKE :search)";
                $params[':search'] = "%$search%";
            }

            $where_clause = "WHERE " . implode(" AND ", $where_conditions);

            // นับจำนวนสินค้าทั้งหมด
            $count_sql = "
                SELECT COUNT(*) as total
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                $where_clause
            ";
            $stmt = $pdo->prepare($count_sql);
            $stmt->execute($params);
            $total_records = $stmt->fetchColumn();
            $total_pages = ceil($total_records / $per_page);

            // ดึงสินค้า
            $sql = "
                SELECT p.*, c.name as category_name, c.slug as category_slug, pi.image_path
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.sort_order = 1
                $where_clause
                ORDER BY p.is_featured DESC, p.created_at DESC
                LIMIT :offset, :per_page
            ";

            $stmt = $pdo->prepare($sql);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            $stmt->bindValue(':per_page', $per_page, PDO::PARAM_INT);
            $stmt->execute();
            $products = $stmt->fetchAll();
        }
    }
} catch (Exception $e) {
    error_log("Database error in products.php: " . $e->getMessage());
}

include 'includes/header.php';
?>

<!-- Products Section -->
<section class="products-section">
    <div class="container">
        <h1 class="page-title">ค้า - GT Sport Design</h1>
        
        <!-- Filter and Search -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-6">
                    <form action="" method="get" class="search-form">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" placeholder="ค้นหาค้า..." value="<?php echo htmlspecialchars($search); ?>">
                            <button type="submit" class="btn btn-primary"><i class="fas fa-search"></i></button>
                        </div>
                    </form>
                </div>
                <div class="col-md-6">
                    <div class="category-filter">
                        <select class="form-select" id="categoryFilter" onchange="filterByCategory(this.value)">
                            <option value="0">หมวด</option>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>" <?php echo ($category_id == $category['id']) ? 'selected' : ''; ?>>
                                <?php echo $category['name']; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Products Grid -->
        <div class="row products-grid">
            <?php if (count($products) > 0): ?>
                <?php foreach ($products as $product): ?>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="product-card">
                        <div class="product-image">
                            <img src="<?= htmlspecialchars($product['image_path'] ?: 'assets/images/no-image.jpg') ?>"
                                 alt="<?= htmlspecialchars($product['name']) ?>"
                                 loading="lazy">
                            <?php if ($product['is_featured']): ?>
                            <span class="badge bg-primary">แนะนำ</span>
                            <?php endif; ?>
                        </div>
                        <div class="product-info">
                            <h3><?= htmlspecialchars($product['name']) ?></h3>
                            <p class="category"><?= htmlspecialchars($product['category_name']) ?></p>
                            <p class="price">฿<?= number_format($product['price'], 0) ?></p>
                            <a href="product-detail.php?id=<?= $product['id'] ?>" class="btn btn-sm btn-primary">ดูรายละเอียด</a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12 text-center py-5">
                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                    <h4>ไม่พบสินค้าที่ค้นหา</h4>
                    <p class="text-muted">กรุณาลองค้นหาด้วยคำอื่น หรือเลือกหมวดหมู่อื่น</p>
                    <a href="products.php" class="btn btn-primary">ดูสินค้าทั้งหมด</a>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page-1; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo $category_id > 0 ? '&category='.$category_id : ''; ?>" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                <?php endif; ?>
                
                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo $category_id > 0 ? '&category='.$category_id : ''; ?>">
                        <?php echo $i; ?>
                    </a>
                </li>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page+1; ?><?php echo !empty($search) ? '&search='.$search : ''; ?><?php echo $category_id > 0 ? '&category='.$category_id : ''; ?>" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
    </div>
</section>

<script>
function filterByCategory(categoryId) {
    let url = new URL(window.location.href);
    url.searchParams.set('category', categoryId);
    url.searchParams.delete('page');
    window.location.href = url.toString();
}
</script>

<?php include 'includes/footer.php'; ?>


