<?php
$host = 'localhost';
$dbname = 'gtsportd_newweb';
$username = 'gtsportd_webgt';
$password = 'XnWC9RdejSeVJMqTPakC';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ การเชื่อมต่อฐานข้อมูลสำเร็จ!";
    
    // ทดสอบสร้างตาราง
    $pdo->exec("CREATE TABLE IF NOT EXISTS test_table (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(50))");
    echo "<br>✅ สามารถสร้างตารางได้!";
    
    // ลบตารางทดสอบ
    $pdo->exec("DROP TABLE test_table");
    echo "<br>✅ ระบบฐานข้อมูลพร้อมใช้งาน!";
    
} catch(PDOException $e) {
    echo "❌ การเชื่อมต่อล้มเหลว: " . $e->getMessage();
}
