<?php
/**
 * GT Sport Design - Complete Database Setup
 * สร้างตารางและข้อมูลเริ่มต้นทั้งหมด
 */

// แสดงข้อผิดพลาดทั้งหมด
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>
<html lang='th'>
<head>
    <meta charset='UTF-8'>
    <title>GT Sport Design - Database Setup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
<div class='container'>
<h1>🚀 GT Sport Design - Database Setup</h1>";

try {
    // เชื่อมต่อฐานข้อมูล
    if (!file_exists('config/database.php')) {
        throw new Exception('ไม่พบไฟล์ config/database.php');
    }
    
    require_once 'config/database.php';
    
    if (!isset($pdo)) {
        throw new Exception('ไม่สามารถเชื่อมต่อฐานข้อมูลได้');
    }
    
    echo "<div class='success'>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</div>";
    
    // อ่านไฟล์ SQL
    $sql_file = 'database/db.sql';
    if (!file_exists($sql_file)) {
        throw new Exception('ไม่พบไฟล์ database/db.sql');
    }
    
    $sql_content = file_get_contents($sql_file);
    if ($sql_content === false) {
        throw new Exception('ไม่สามารถอ่านไฟล์ database/db.sql ได้');
    }
    
    echo "<div class='info'>📖 อ่านไฟล์ SQL สำเร็จ</div>";
    
    // แยก SQL statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql_content)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    echo "<div class='step'>";
    echo "<h3>🔧 กำลังสร้างตารางและข้อมูล...</h3>";
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($statements as $statement) {
        try {
            $pdo->exec($statement);
            
            // ตรวจสอบประเภทของ statement
            if (preg_match('/CREATE TABLE.*`(\w+)`/i', $statement, $matches)) {
                echo "<div class='success'>✅ สร้างตาราง {$matches[1]} สำเร็จ</div>";
            } elseif (preg_match('/INSERT INTO.*`(\w+)`/i', $statement, $matches)) {
                echo "<div class='success'>✅ เพิ่มข้อมูลในตาราง {$matches[1]} สำเร็จ</div>";
            }
            
            $success_count++;
        } catch (PDOException $e) {
            // ข้ามข้อผิดพลาดที่ไม่สำคัญ
            if (strpos($e->getMessage(), 'already exists') !== false || 
                strpos($e->getMessage(), 'Duplicate entry') !== false) {
                continue;
            }
            
            echo "<div class='error'>❌ ข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</div>";
            $error_count++;
        }
    }
    
    echo "</div>";
    
    // ตรวจสอบตารางที่สร้างแล้ว
    echo "<div class='step'>";
    echo "<h3>📊 ตรวจสอบตารางที่สร้างแล้ว</h3>";
    
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_tables = [
        'admins', 'categories', 'customers', 'products', 'product_images',
        'orders', 'order_items', 'gallery_images', 'reviews', 'bookings',
        'designs', 'customer_designs', 'system_settings', 'pickup_locations',
        'file_uploads'
    ];
    
    foreach ($required_tables as $table) {
        if (in_array($table, $tables)) {
            // นับจำนวนข้อมูลในตาราง
            $count_stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
            $count = $count_stmt->fetchColumn();
            echo "<div class='success'>✅ ตาราง $table มีข้อมูล $count รายการ</div>";
        } else {
            echo "<div class='error'>❌ ไม่พบตาราง $table</div>";
        }
    }
    
    echo "</div>";
    
    // สรุปผล
    echo "<div class='step'>";
    echo "<h3>📈 สรุปผลการติดตั้ง</h3>";
    echo "<div class='success'>✅ ดำเนินการสำเร็จ: $success_count รายการ</div>";
    if ($error_count > 0) {
        echo "<div class='error'>❌ ข้อผิดพลาด: $error_count รายการ</div>";
    }
    echo "<div class='info'>🎉 การติดตั้งฐานข้อมูลเสร็จสมบูรณ์!</div>";
    echo "</div>";
    
    // ลิงก์ไปหน้าหลัก
    echo "<div style='text-align: center; margin-top: 30px;'>";
    echo "<a href='index.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>🏠 ไปหน้าหลัก</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</div>";
    echo "<div class='info'>💡 กรุณาตรวจสอบการตั้งค่าฐานข้อมูลใน config/database.php</div>";
}

echo "</div></body></html>";
?>
