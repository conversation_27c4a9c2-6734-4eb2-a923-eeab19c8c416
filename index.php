<?php
session_start();

// ข้อมูล fallback สำหรับ gallery, reviews และ products
$gallery_items = [];
$reviews = [];
$featured_products = [];
$site_settings = [];

// ลองเชื่อมต่อฐานข้อมูลเพื่อดึงข้อมูล
try {
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';

        if (isset($pdo)) {
            // ดึงข้อมูลการตั้งค่าระบบ
            $stmt = $pdo->query("SELECT setting_key, setting_value FROM system_settings");
            $settings_data = $stmt->fetchAll();
            foreach ($settings_data as $setting) {
                $site_settings[$setting['setting_key']] = $setting['setting_value'];
            }

            // ดึงข้อมูล gallery
            $stmt = $pdo->query("SELECT * FROM gallery_images WHERE is_active = 1 ORDER BY sort_order LIMIT 8");
            $gallery_items = $stmt->fetchAll();

            // ดึงข้อมูล reviews
            $stmt = $pdo->query("
                SELECT r.*, c.name as customer_name, p.name as product_name
                FROM reviews r
                LEFT JOIN customers c ON r.customer_id = c.id
                LEFT JOIN products p ON r.product_id = p.id
                WHERE r.status = 'approved' AND r.rating >= 4
                ORDER BY r.is_featured DESC, r.created_at DESC
                LIMIT 6
            ");
            $reviews = $stmt->fetchAll();

            // ดึงข้อมูลสินค้าแนะนำ
            $stmt = $pdo->query("
                SELECT p.*, c.name as category_name, pi.image_path
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.sort_order = 1
                WHERE p.is_featured = 1 AND p.is_active = 1
                ORDER BY p.id
                LIMIT 6
            ");
            $featured_products = $stmt->fetchAll();
        }
    }
} catch (Exception $e) {
    // ไม่ต้องแสดงข้อผิดพลาด ใช้ข้อมูล fallback
    error_log("Database error in index.php: " . $e->getMessage());
}

// ข้อมูล fallback สำหรับการตั้งค่า
if (empty($site_settings)) {
    $site_settings = [
        'site_name' => 'GT Sport Design',
        'contact_phone' => '************',
        'contact_line' => '@gtsport',
        'facebook_page' => 'https://www.facebook.com/GTSportDesign.1',
        'primary_color' => '#ee501b',
        'secondary_color' => '#ff6b35'
    ];
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>GT Sport Design - ผู้เชี่ยวชาญด้านการออกแบบและผลิตเสื้อกีฬา</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="ผู้เชี่ยวชาญด้านการออกแบบและผลิตเสื้อกีฬาคุณภาพสูง พร้อมเทคโนโลยีการออกแบบที่ทันสมัย">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #ee501b;
            --secondary-color: #ff6b35;
        }

        .navbar-brand { color: var(--primary-color) !important; font-weight: bold; font-size: 1.5rem; }
        .btn-primary { background: var(--primary-color); border: none; }
        .btn-primary:hover { background: #d44615; }
        .contact-card { border: none; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border-radius: 15px; }
        .contact-icon { color: var(--primary-color); font-size: 2rem; }
        .map-container { height: 300px; border-radius: 15px; overflow: hidden; }
        .chat-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 300px;
            max-height: 400px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 25px rgba(0,0,0,0.3);
            z-index: 1000;
            display: none;
        }
        .chat-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: var(--primary-color);
            border-radius: 50%;
            border: none;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            z-index: 1001;
        }
        .chat-header {
            background: var(--primary-color);
            color: white;
            padding: 15px;
            border-radius: 15px 15px 0 0;
        }
        .chat-messages {
            height: 250px;
            overflow-y: auto;
            padding: 15px;
        }
        .contact-hero {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 80px 0;
        }
    </style>
</head>

<body>
<?php include 'includes/header.php'; ?>

	 <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: sans-serif;
    }

    .hero-section {
      width: 100%;
    }

    .desktop,
    .mobile {
      width: 100%;
    }

    .desktop {
      display: block;
    }

    .mobile {
      display: none;
    }

    img {
      width: 100%;
      height: auto;
      display: block;
    }
	.hero-section,
    .desktop,
    .mobile {
  margin: 0 !important;
  padding: 0 !important;
  line-height: 0;
}

    @media (max-width: 768px) {
      .desktop {
        display: none;
      }

      .mobile {
        display: block;
      }
	  .mobile img {
        max-height: 400px;
        object-fit: cover;
}
    }
  </style>

    <!-- แสดงบน Desktop -->
    <div class="desktop">
      <img src="assets/images/banner/3.jpg" alt="Banner for Desktop">
    </div>

    <!-- แสดงบน Mobile -->
    <div class="mobile">
      <img src="assets/images/banner/3.jpg" alt="Banner for Mobile">
    </div>




 <!-- Products Section -->
        <section class="products-section" id="products"style="">
            <div class="container"style="margin-top:0px;">
               

               
<div class="section" style="padding: 40px 20px; background-color: #f5f5f5;">
    <div class="container" style="max-width: 1200px; margin: 0 auto;">
        <h2 style="font-size: 32px; text-align: center;">
            ค้นพบเสื้อที่ใช่ <span style="color: #e63946;">สำหรับทุกไลฟ์สไตล์</span>
        </h2>
        <p style="text-align: center; font-size: 16px; margin-bottom: 40px;">
            ผลิตเสื้อด้วยผ้าคุณภาพพรีเมี่ยม มีเนื้อผ้าให้เลือกหลากหลายชนิด<br>
            ใส่ใจในทุกรายละเอียด พร้อมจัดส่งทั่วประเทศ
        </p>

        <div class="product-grid" style="display: flex; flex-wrap: wrap; gap: 20px; justify-content: center;">
            <?php
            // ใช้ข้อมูลจากฐานข้อมูลก่อน ถ้าไม่มีใช้ fallback
            if (!empty($featured_products)) {
                foreach ($featured_products as $product): ?>
                    <div class="product-card" style="width: 300px; background: #fff; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">
                        <a href="product-detail.php?id=<?= $product['id'] ?>" style="text-decoration: none; color: inherit;">
                            <div class="image" style="background-image: url('<?= $product['image_path'] ?: 'assets/images/no-image.jpg' ?>'); height: 200px; background-size: cover; background-position: center;"></div>
                            <div class="content" style="padding: 20px;">
                                <h3 style="font-size: 20px; margin: 0 0 10px;"><?= htmlspecialchars($product['name']) ?></h3>
                                <p style="font-size: 14px; color: #666; margin-bottom: 10px;"><?= htmlspecialchars($product['description']) ?></p>
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span style="color: #ee501b; font-weight: bold; font-size: 18px;">฿<?= number_format($product['price'], 0) ?></span>
                                    <span style="color: #999; font-size: 12px;"><?= htmlspecialchars($product['category_name']) ?></span>
                                </div>
                            </div>
                        </a>
                    </div>
                <?php endforeach;
            } else {
                // Fallback data
                $fallback_items = [
                    [
                        'title' => 'เสื้อบริษัท',
                        'desc' => 'เสื้อบริษัท สะท้อนความเป็นมืออาชีพ นำเสนอสไตล์ของคุณ พร้อมให้คุณดูดีในทุกวัน',
                        'img' => 'uploads/products/475382494_122220623120074738_924515879995798050_n.jpg',
                        'price' => '350',
                        'link' => 'shirt-design.php#company_design'
                    ],
                    [
                        'title' => 'เสื้อกิจกรรม',
                        'desc' => 'พร้อมสำหรับทุกความสนุกด้วยเสื้อกิจกรรม ที่ให้คุณดูโดดเด่นและรู้สึกสบายทุกการเคลื่อนไหว',
                        'img' => 'uploads/products/475670051_122220623066074738_2795502938249037331_n.jpg',
                        'price' => '320',
                        'link' => 'shirt-design.php#activities_design'
                    ],
                    [
                        'title' => 'เสื้อกีฬาสี โรงเรียน',
                        'desc' => 'โชว์พลังทีมด้วยเสื้อกีฬาสีที่สร้างสรรค์เพื่อความสนุกและความสบายในการแข่งขัน',
                        'img' => 'uploads/products/475697812_122220623084074738_101296545669764346_n.jpg',
                        'price' => '280',
                        'link' => 'shirt-design.php#school_design'
                    ],
                    [
                        'title' => 'เสื้อกีฬา',
                        'desc' => 'เสื้อกีฬาที่ทำให้คุณรู้สึกสบาย มั่นใจในทุกการเคลื่อนไหว',
                        'img' => 'uploads/products/475755576_122220623090074738_8040878751312398557_n.jpg',
                        'price' => '300',
                        'link' => 'shirt-design.php#sport_design'
                    ],
                    [
                        'title' => 'เสื้อหน่วยงานราชการ',
                        'desc' => 'เสื้อหน่วยงานราชการ บริการไว คุณภาพสูง ประทับใจทุกหน่วยงาน',
                        'img' => 'uploads/products/475815335_122220623102074738_309201310605034286_n.jpg',
                        'price' => '380',
                        'link' => 'shirt-design.php#government_design'
                    ],
                    [
                        'title' => 'เสื้อดีไซน์พิเศษ',
                        'desc' => 'ออกแบบพิเศษเฉพาะคุณ กับดีไซน์ที่ไม่ซ้ำใคร',
                        'img' => 'uploads/products/475925098_122220623750074738_1631174588409703078_n.jpg',
                        'price' => '450',
                        'link' => 'shirt-design.php#special_design'
                    ],
                ];

                foreach ($fallback_items as $item): ?>
                    <div class="product-card" style="width: 300px; background: #fff; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">
                        <a href="<?= $item['link'] ?>" style="text-decoration: none; color: inherit;">
                            <div class="image" style="background-image: url('<?= $item['img'] ?>'); height: 200px; background-size: cover; background-position: center;"></div>
                            <div class="content" style="padding: 20px;">
                                <h3 style="font-size: 20px; margin: 0 0 10px;"><?= $item['title'] ?></h3>
                                <p style="font-size: 14px; color: #666; margin-bottom: 10px;"><?= $item['desc'] ?></p>
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span style="color: #ee501b; font-weight: bold; font-size: 18px;">฿<?= $item['price'] ?></span>
                                </div>
                            </div>
                        </a>
                    </div>
                <?php endforeach;
            } ?>
        </div>
    </div>
</div>

                <div class="text-center mt-5">
                    <a href="products.php" class="btn-hero">ดูสินค้าทั้งหมด</a>
                </div>
            </div>
        </section>


        <!-- Features Section -->
        <section class="features-section" id="features"style="">
            <div class="container">
              <h2 style="font-size: 32px; text-align: center;">
            ทำไมต้อง  <span style="color:rgb(255, 255, 255);">GT SPORT DESIGN</span>
        </h2>
        <p style="text-align: center; font-size: 16px; margin-bottom: 40px;">
            เหตุผลที่ลูกค้าเลือกใช้บริการของเรา
        </p>
        <div class="row g-4">
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-shipping-fast"></i>
                            </div>
                            <h5 class="feature-title">ส่งชิ้นผ้า<br>ให้ฟรี!</h5>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <h5 class="feature-title">ตอบแชทไว<br>ภายใน 5 นาที</h5>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-tshirt"></i>
                            </div>
                            <h5 class="feature-title">ปรับแบบ<br>จนพอใจ</h5>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-award"></i>
                            </div>
                            <h5 class="feature-title">ทุกโลโก้<br>ราคาเดียว</h5>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                            <h5 class="feature-title">รวม VAT<br>แล้วไม่บวกเพิ่ม</h5>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <h5 class="feature-title">รับประกันสินค้า<br>เคลมฟรีใน 7 วัน</h5>
                        </div>
                    </div>
                </div>
            </div>
        </section>

       
        <!-- Gallery Section -->
        <section class="gallery-section" id="gallery"style="">
            <div class="container"style="margin-top:0px;">
                <div class="section-title">
                    <h2>รีวิวความไว้ใจจาก <span class="highlight">GT SPORT DESIGN</span></h2>
                    <p class="lead">ผลงานการออกแบบและผลิตเสื้อกีฬาของเรา</p>
                </div>

                <div class="gallery-grid">
                    <?php if (!empty($gallery_items)): ?>
                        <?php foreach (array_slice($gallery_items, 0, 8) as $item): ?>
                            <div class="gallery-item">
                                <img src="<?php echo htmlspecialchars($item['image_path']); ?>"
                                    alt="<?php echo htmlspecialchars($item['title'] ?: 'ผลงานการออกแบบ'); ?>"
                                    loading="lazy">
                                <div class="gallery-overlay">
                                    <span><?php echo htmlspecialchars($item['title'] ?: 'ผลงานการออกแบบ'); ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <!-- Fallback gallery -->
                        <?php
                        $fallback_gallery = [
                            ['img' => 'https://img2.pic.in.th/pic/LINE_ALBUM__241018_1.jpg', 'title' => 'ผลงานการออกแบบ 1'],
                            ['img' => 'https://img5.pic.in.th/file/secure-sv1/LINE_ALBUM__241018_2.jpg', 'title' => 'ผลงานการออกแบบ 2'],
                            ['img' => 'https://img5.pic.in.th/file/secure-sv1/LINE_ALBUM__241018_3755795ed32b03ea7.jpg', 'title' => 'ผลงานการออกแบบ 3'],
                            ['img' => 'https://img5.pic.in.th/file/secure-sv1/LINE_ALBUM__241018_5.jpg', 'title' => 'ผลงานการออกแบบ 4'],
                            ['img' => 'https://img5.pic.in.th/file/secure-sv1/644002183119016645_n.jpg', 'title' => 'ผลงานการออกแบบ 5'],
                            ['img' => 'https://img2.pic.in.th/pic/LINE_ALBUM__241018_7.jpg', 'title' => 'ผลงานการออกแบบ 6'],
                            ['img' => 'https://img2.pic.in.th/pic/LINE_ALBUM__241018_8.jpg', 'title' => 'ผลงานการออกแบบ 7'],
                            ['img' => 'https://img2.pic.in.th/pic/LINE_ALBUM__241018_10.jpg', 'title' => 'ผลงานการออกแบบ 8']
                        ];
                        foreach ($fallback_gallery as $item): ?>
                            <div class="gallery-item">
                                <img src="<?= $item['img'] ?>" alt="<?= $item['title'] ?>" loading="lazy">
                                <div class="gallery-overlay">
                                    <span><?= $item['title'] ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <div class="text-center mt-5">
                    <a href="gallery.php" class="btn-hero">ดูผลงานเพิ่มเติม</a>
                </div>
            </div>
        </section>

        <!-- Reviews Section -->
        <section class="reviews-section" id="reviews">
            <div class="container">
                <div class="section-title">
                    <h2>ความคิดเห็นจาก<span class="highlight"><h2 style="color: rgb(19, 20, 20);">ลูกค้า</h2></span></h2>
                    <p class="lead"style="color: rgb(19, 20, 20);">รีวิวจากลูกค้าที่ใช้บริการจริง</p>
                </div>

                <div class="row g-4">
                    <?php if (!empty($reviews)): ?>
                        <?php foreach (array_slice($reviews, 0, 3) as $review): ?>
                            <div class="col-lg-4">
                                <div class="review-card">
                                    <div class="review-stars">
                                        <?php for ($i = 0; $i < $review['rating']; $i++): ?>
                                            <i class="fas fa-star"></i>
                                        <?php endfor; ?>
                                        <?php for ($i = $review['rating']; $i < 5; $i++): ?>
                                            <i class="far fa-star"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <p class="review-text"><?php echo htmlspecialchars($review['comment']); ?></p>
                                    <div class="review-author">
                                        <div class="review-avatar">
                                            <?php echo strtoupper(substr($review['customer_name'] ?: 'A', 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="review-name">
                                                <?php echo htmlspecialchars($review['customer_name'] ?: 'ลูกค้า'); ?></div>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($review['product_name'] ?: 'สินค้าของเรา'); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <!-- Fallback reviews -->
                        <?php
                        $fallback_reviews = [
                            ['name' => 'สมชาย ใจดี', 'comment' => 'ใส่สบาย ลายสวย คุณภาพดีมาก แนะนำเลยครับ', 'rating' => 5, 'product' => 'เสื้อกีฬาทีม'],
                            ['name' => 'zeza', 'comment' => 'เสื้อคุณภาพดีมาก ผ้านุ่ม ใส่สบาย', 'rating' => 5, 'product' => 'เสื้อกีฬาทีม'],
                            ['name' => 'baet', 'comment' => 'ทีมงานบริการดีเยี่ยม ส่งไว คุณภาพดี', 'rating' => 5, 'product' => 'เสื้อกีฬาทีม']
                        ];
                        foreach ($fallback_reviews as $review): ?>
                            <div class="col-lg-4">
                                <div class="review-card">
                                    <div class="review-stars">
                                        <?php for ($i = 0; $i < $review['rating']; $i++): ?>
                                            <i class="fas fa-star"></i>
                                        <?php endfor; ?>
                                        <?php for ($i = $review['rating']; $i < 5; $i++): ?>
                                            <i class="far fa-star"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <p class="review-text"><?= htmlspecialchars($review['comment']) ?></p>
                                    <div class="review-author">
                                        <div class="review-avatar"><?= strtoupper(substr($review['name'], 0, 1)) ?></div>
                                        <div>
                                            <div class="review-name"><?= htmlspecialchars($review['name']) ?></div>
                                            <small class="text-muted"><?= htmlspecialchars($review['product']) ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </section>

               
                <div class="elementor-element elementor-element-9f99210 e-flex e-con-boxed e-con e-parent e-lazyloaded" data-id="9f99210" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
					<div class="e-con-inner">
				<div class="elementor-element elementor-element-3de93a1 elementor-widget elementor-widget-heading animated fadeIn" data-id="3de93a1" data-element_type="widget" data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}" data-widget_type="heading.default">
				<div class="elementor-widget-container">
					<h2 class="elementor-heading-title elementor-size-default">VIDEO</h2>				</div>
				</div>
		<div class="elementor-element elementor-element-0394c0f e-grid e-con-full e-con e-child" data-id="0394c0f" data-element_type="container">
				<div class="elementor-element elementor-element-e362bf7 elementor-widget elementor-widget-video" data-id="e362bf7" data-element_type="widget" data-settings="{&quot;youtube_url&quot;:&quot;https:\/\/www.youtube.com\/watch?v=XHOmBV4js_E&quot;,&quot;video_type&quot;:&quot;youtube&quot;,&quot;controls&quot;:&quot;yes&quot;}" data-widget_type="video.default">
				<div class="elementor-widget-container">
							<div class="elementor-wrapper elementor-open-inline">
			<iframe class="elementor-video" frameborder="0" allowfullscreen="" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" title="Video Placeholder" width="640" height="360" src="https://www.youtube.com/embed/XHOmBV4js_E?controls=1&amp;rel=0&amp;playsinline=0&amp;cc_load_policy=0&amp;autoplay=0&amp;enablejsapi=1&amp;origin=https%3A%2F%2Fwww.finixsports.com&amp;widgetid=1&amp;forigin=https%3A%2F%2Fwww.finixsports.com%2F&amp;aoriginsup=1&amp;gporigin=https%3A%2F%2Fwww.finixsports.com%2Fshirt-design%2F&amp;vf=1" id="widget2" data-gtm-yt-inspected-14="true"></iframe>		</div>
						</div>
				</div>
		
               
        <?php include './includes/footer.php'; ?>

            <!-- Live Chat Widget -->
    <button class="chat-toggle" onclick="toggleChat()">
        <i class="fas fa-comments"></i>
    </button>

    <div class="chat-widget" id="chatWidget">
        <div class="chat-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0"><i class="fas fa-comments"></i> แชทสด</h6>
                <button class="btn btn-sm text-white" onclick="toggleChat()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="chat-messages" id="chatMessages">
            <div class="text-center text-muted py-3">
                <small>พิมพ์ข้อความเพื่อเริ่มการสนทนา</small>
            </div>
        </div>
        <div class="p-3">
            <div class="input-group">
                <input type="text" class="form-control" id="chatInput" placeholder="พิมพ์ข้อความ..."
                       onkeypress="if(event.key==='Enter') sendMessage()">
                <button class="btn btn-primary" onclick="sendMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let chatOpen = false;

        function toggleChat() {
            const widget = document.getElementById('chatWidget');
            const toggle = document.querySelector('.chat-toggle');

            if (chatOpen) {
                widget.style.display = 'none';
                toggle.innerHTML = '<i class="fas fa-comments"></i>';
                chatOpen = false;
            } else {
                widget.style.display = 'block';
                toggle.innerHTML = '<i class="fas fa-times"></i>';
                chatOpen = true;

                // Auto message
                if (!localStorage.getItem('chatWelcome')) {
                    setTimeout(() => {
                        addMessage('<?= $site_settings['site_name'] ?>', 'สวัสดีครับ! มีอะไรให้เราช่วยไหมครับ?', 'admin');
                        localStorage.setItem('chatWelcome', 'shown');
                    }, 1000);
                }
            }
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (message) {
                addMessage('คุณ', message, 'user');
                input.value = '';

                // Auto reply
                setTimeout(() => {
                    const replies = [
                        'ขอบคุณสำหรับข้อความครับ เราจะติดต่อกลับไปเร็วๆ นี้',
                        'สำหรับข้อมูลเพิ่มเติม สามารถโทร <?= $site_settings['contact_phone'] ?> ได้เลยครับ',
                        'หรือสามารถ LINE มาที่ <?= $site_settings['contact_line'] ?> ได้ครับ'
                    ];
                    const reply = replies[Math.floor(Math.random() * replies.length)];
                    addMessage('<?= $site_settings['site_name'] ?>', reply, 'admin');
                }, 1500);
            }
        }

        function addMessage(sender, text, type) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `mb-2 ${type === 'user' ? 'text-end' : ''}`;

            messageDiv.innerHTML = `
                <div class="d-inline-block p-2 rounded ${type === 'user' ? 'bg-primary text-white' : 'bg-light'}">
                    <small class="fw-bold">${sender}</small><br>
                    <span>${text}</span>
                </div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Auto-resize contact form
        document.addEventListener('DOMContentLoaded', function() {
            const textareas = document.querySelectorAll('textarea');
            textareas.forEach(textarea => {
                textarea.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = this.scrollHeight + 'px';
                });
            });
        });
    </script>

