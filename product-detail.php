<?php
session_start();
require_once 'config/database.php';
$pdo = getDbConnection();

// ตรวจสอบ ID ร้านค้า
$product_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
if ($product_id <= 0) {
    header('Location: products.php');
    exit;
}

// ข้อมูลร้านค้า
$stmt = $pdo->prepare("SELECT p.*, c.name as category_name FROM products p 
                       LEFT JOIN categories c ON p.category_id = c.id 
                       WHERE p.id = ? AND p.status = 'active'");
$stmt->execute([$product_id]);
$product = $stmt->fetch();

if (!$product) {
    header('Location: products.php');
    exit;
}

// ภาพของร้านค้า
$stmt = $pdo->prepare("SELECT * FROM product_images WHERE product_id = ?");
$stmt->execute([$product_id]);
$product_images = $stmt->fetchAll();

// ร้านค้าเกี่ยวข้อง
$stmt = $pdo->prepare("SELECT * FROM products 
                       WHERE category_id = ? AND id != ? AND status = 'active' 
                       ORDER BY RAND() LIMIT 4");
$stmt->execute([$product['category_id'], $product_id]);
$related_products = $stmt->fetchAll();

// ของร้านค้า
$stmt = $pdo->prepare("SELECT r.*, c.fullname, c.profile_image FROM product_reviews r 
                       JOIN customers c ON r.customer_id = c.id 
                       WHERE r.product_id = ? AND r.status = 'approved' 
                       ORDER BY r.created_at DESC");
$stmt->execute([$product_id]);
$reviews = $stmt->fetchAll();

// คำนวณคะแนนเฉื่อย
$avg_rating = 0;
if (count($reviews) > 0) {
    $total_rating = 0;
    foreach ($reviews as $review) {
        $total_rating += $review['rating'];
    }
    $avg_rating = $total_rating / count($reviews);
}

include 'includes/header.php';
?>

<!-- Product Detail Section -->
<section class="product-detail-section">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">หน้าแรก</a></li>
                <li class="breadcrumb-item"><a href="products.php">ร้านค้า</a></li>
                <li class="breadcrumb-item"><a href="products.php?category=<?php echo $product['category_id']; ?>"><?php echo $product['category_name']; ?></a></li>
                <li class="breadcrumb-item active" aria-current="page"><?php echo $product['name']; ?></li>
            </ol>
        </nav>
        
        <div class="row">
            <!-- Product Images -->
            <div class="col-md-6">
                <div class="product-images">
                    <div class="main-image">
                        <img src="uploads/products/<?php echo $product['image']; ?>" alt="<?php echo $product['name']; ?>" id="mainProductImage">
                    </div>
                    <?php if (count($product_images) > 0): ?>
                    <div class="thumbnail-images">
                        <div class="thumbnail active" onclick="changeMainImage('uploads/products/<?php echo $product['image']; ?>')">
                            <img src="uploads/products/<?php echo $product['image']; ?>" alt="<?php echo $product['name']; ?>">
                        </div>
                        <?php foreach ($product_images as $image): ?>
                        <div class="thumbnail" onclick="changeMainImage('uploads/products/<?php echo $image['image']; ?>')">
                            <img src="uploads/products/<?php echo $image['image']; ?>" alt="<?php echo $product['name']; ?>">
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Product Info -->
            <div class="col-md-6">
                <div class="product-info">
                    <h1 class="product-title"><?php echo $product['name']; ?></h1>
                    
                    <div class="product-meta">
                        <span class="product-category">หมวดหมู่: <?php echo $product['category_name']; ?></span>
                        <div class="product-rating">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <?php if ($i <= round($avg_rating)): ?>
                                <i class="fas fa-star"></i>
                                <?php else: ?>
                                <i class="far fa-star"></i>
                                <?php endif; ?>
                            <?php endfor; ?>
                            <span>(<?php echo count($reviews); ?> รีวิว)</span>
                        </div>
                    </div>
                    
                    <div class="product-price">
                        <span class="price"><?php echo number_format($product['price']); ?> บาท</span>
                        <?php if ($product['old_price'] > 0 && $product['old_price'] > $product['price']): ?>
                        <span class="old-price"><?php echo number_format($product['old_price']); ?> บาท</span>
                        <span class="discount">-<?php echo round(($product['old_price'] - $product['price']) / $product['old_price'] * 100); ?>%</span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="product-description">
                        <?php echo nl2br($product['description']); ?>
                    </div>
                    
                    <?php if ($product['stock'] > 0): ?>
                    <div class="product-stock">
                        <span class="in-stock">ค้า</span>
                    </div>
                    <?php else: ?>
                    <div class="product-stock">
                        <span class="out-of-stock">ค้าหมด</span>
                    </div>
                    <?php endif; ?>
                    
                    <div class="product-actions">
                        <form action="cart.php" method="post">
                            <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                            <div class="quantity-selector">
                                <label for="quantity">จำนวน:</label>
                                <div class="quantity-controls">
                                    <button type="button" class="quantity-btn minus" onclick="decreaseQuantity()">-</button>
                                    <input type="number" id="quantity" name="quantity" value="1" min="1" max="<?php echo $product['stock']; ?>">
                                    <button type="button" class="quantity-btn plus" onclick="increaseQuantity(<?php echo $product['stock']; ?>)">+</button>
                                </div>
                            </div>
                            
                            <div class="action-buttons">
                                <button type="submit" name="add_to_cart" class="btn btn-primary" <?php echo ($product['stock'] <= 0) ? 'disabled' : ''; ?>>
                                    <i class="fas fa-shopping-cart"></i> เลือมลงตะกร้า
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="window.location.href='shirt-design.php?product_id=<?php echo $product['id']; ?>'">
                                    <i class="fas fa-palette"></i> ออกแบบเสื้อ
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Product Tabs -->
        <div class="product-tabs mt-5">
            <ul class="nav nav-tabs" id="productTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab" aria-controls="details" aria-selected="true">รายละเอียด</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button" role="tab" aria-controls="reviews" aria-selected="false">รีวิว (<?php echo count($reviews); ?>)</button>
                </li>
            </ul>
            <div class="tab-content" id="productTabsContent">
                <div class="tab-pane fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
                    <div class="product-details">
                        <?php echo nl2br($product['details']); ?>
                    </div>
                </div>
                <div class="tab-pane fade" id="reviews" role="tabpanel" aria-labelledby="reviews-tab">
                    <div class="product-reviews">
                        <?php if (count($reviews) > 0): ?>
                            <?php foreach ($reviews as $review): ?>
                            <div class="review-item">
                                <div class="review-header">
                                    <?php if (!empty($review['profile_image'])): ?>
                                    <img src="uploads/profiles/<?php echo $review['profile_image']; ?>" alt="<?php echo $review['fullname']; ?>" class="reviewer-avatar">
                                    <?php else: ?>
                                    <img src="assets/images/default-avatar.png" alt="<?php echo $review['fullname']; ?>" class="reviewer-avatar">
                                    <?php endif; ?>
                                    <div class="reviewer-info">
                                        <h5><?php echo $review['fullname']; ?></h5>
                                        <div class="review-rating">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <?php if ($i <= $review['rating']): ?>
                                                <i class="fas fa-star"></i>
                                                <?php else: ?>
                                                <i class="far fa-star"></i>
                                                <?php endif; ?>
                                            <?php endfor; ?>
                                        </div>
                                        <span class="review-date"><?php echo date('d/m/Y', strtotime($review['created_at'])); ?></span>
                                    </div>
                                </div>
                                <div class="review-content">
                                    <p><?php echo nl2br($review['comment']); ?></p>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="no-reviews">ไม่มีรีวิว</p>
                        <?php endif; ?>
                        
                        <?php if (isset($_SESSION['customer_id'])): ?>
                        <div class="write-review">
                            <h4>เขียนรีวิว</h4>
                            <form action="submit_review.php" method="post">
                                <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                <div class="mb-3">
                                    <label for="rating" class="form-label">คะแนน</label>
                                    <div class="rating-select">
                                        <div class="stars">
                                            <i class="far fa-star" data-rating="1"></i>
                                            <i class="far fa-star" data-rating="2"></i>
                                            <i class="far fa-star" data-rating="3"></i>
                                            <i class="far fa-star" data-rating="4"></i>
                                            <i class="far fa-star" data-rating="5"></i>
                                        </div>
                                        <input type="hidden" name="rating" id="rating" value="0" required>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="comment" class="form-label">ความคิดเห็น</label>
                                    <textarea class="form-control" id="comment" name="comment" rows="3" required></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">ส่ง</button>
                            </form>
                        </div>
                        <?php else: ?>
                        <div class="login-to-review">
                            <p>คุณต้อง <a href="login.php">เข้าสู่ระบบ</a> เพื่อเขียนรีวิว</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Related Products -->
        <?php if (count($related_products) > 0): ?>
        <div class="related-products mt-5">
            <h3 class="section-title">ร้านค้าเกี่ยวข้อง</h3>
            <div class="row">
                <?php foreach ($related_products as $related): ?>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="product-card">
                        <div class="product-image">
                            <img src="uploads/products/<?php echo $related['image']; ?>" alt="<?php echo $related['name']; ?>">
                        </div>
                        <div class="product-info">
                            <h3><?php echo $related['name']; ?></h3>
                            <p class="price"><?php echo number_format($related['price']); ?> บาท</p>
                            <a href="product-detail.php?id=<?php echo $related['id']; ?>" class="btn btn-sm btn-primary">ดูรายละเอียด</a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<script>
function changeMainImage(imageSrc) {
    document.getElementById('mainProductImage').src = imageSrc;
    
    // Update active thumbnail
    const thumbnails = document.querySelectorAll('.thumbnail');
    thumbnails.forEach(thumb => {
        if (thumb.querySelector('img').src === imageSrc) {
            thumb.classList.add('active');
        } else {
            thumb.classList.remove('active');
        }
    });
}

function decreaseQuantity() {
    const quantityInput = document.getElementById('quantity');
    if (quantityInput.value > 1) {
        quantityInput.value = parseInt(quantityInput.value) - 1;
    }
}

function increaseQuantity(maxStock) {
    const quantityInput = document.getElementById('quantity');
    if (parseInt(quantityInput.value) < maxStock) {
        quantityInput.value = parseInt(quantityInput.value) + 1;
    }
}

// Star rating functionality
document.addEventListener('DOMContentLoaded', function() {
    const stars = document.querySelectorAll('.stars i');
    const ratingInput = document.getElementById('rating');
    
    stars.forEach(star => {
        star.addEventListener('mouseover', function() {
            const rating = this.getAttribute('data-rating');
            highlightStars(rating);
        });
        
        star.addEventListener('mouseout', function() {
            const currentRating = ratingInput.value;
            highlightStars(currentRating);
        });
        
        star.addEventListener('click', function() {
            const rating = this.getAttribute('data-rating');
            ratingInput.value = rating;
            highlightStars(rating);
        });
    });
    
    function highlightStars(rating) {
        stars.forEach(star => {
            const starRating = star.getAttribute('data-rating');
            if (starRating <= rating) {
                star.classList.remove('far');
                star.classList.add('fas');
            } else {
                star.classList.remove('fas');
                star.classList.add('far');
            }
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>

