<?php
session_start();

$product_id = $_GET['id'] ?? 0;
$product = null;
$product_images = [];
$related_products = [];
$reviews = [];

// ดึงข้อมูลสินค้าจากฐานข้อมูล
try {
    if (file_exists('config/database.php') && $product_id) {
        require_once 'config/database.php';

        if (isset($pdo)) {
            // ดึงข้อมูลสินค้า
            $stmt = $pdo->prepare("
                SELECT p.*, c.name as category_name, c.slug as category_slug
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.id = :id AND p.is_active = 1
            ");
            $stmt->bindParam(':id', $product_id, PDO::PARAM_INT);
            $stmt->execute();
            $product = $stmt->fetch();

            if ($product) {
                // ดึงรูปภาพสินค้า
                $stmt = $pdo->prepare("SELECT * FROM product_images WHERE product_id = :id ORDER BY sort_order");
                $stmt->bindParam(':id', $product_id, PDO::PARAM_INT);
                $stmt->execute();
                $product_images = $stmt->fetchAll();

                // ดึงสินค้าที่เกี่ยวข้อง
                $stmt = $pdo->prepare("
                    SELECT p.*, pi.image_path
                    FROM products p
                    LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.sort_order = 1
                    WHERE p.category_id = :category_id AND p.id != :id AND p.is_active = 1
                    ORDER BY p.is_featured DESC, RAND()
                    LIMIT 4
                ");
                $stmt->bindParam(':category_id', $product['category_id'], PDO::PARAM_INT);
                $stmt->bindParam(':id', $product_id, PDO::PARAM_INT);
                $stmt->execute();
                $related_products = $stmt->fetchAll();

                // ดึงรีวิว
                $stmt = $pdo->prepare("
                    SELECT r.*, c.name as customer_name
                    FROM reviews r
                    LEFT JOIN customers c ON r.customer_id = c.id
                    WHERE r.product_id = :id AND r.status = 'approved'
                    ORDER BY r.created_at DESC
                ");
                $stmt->bindParam(':id', $product_id, PDO::PARAM_INT);
                $stmt->execute();
                $reviews = $stmt->fetchAll();
            }
        }
    }
} catch (Exception $e) {
    error_log("Database error in product-detail.php: " . $e->getMessage());
}

// ถ้าไม่พบสินค้า redirect ไปหน้า products
if (!$product) {
    header('Location: products.php');
    exit;
}

// คำนวณคะแนนเฉลี่ย
$avg_rating = 0;
if (count($reviews) > 0) {
    $total_rating = 0;
    foreach ($reviews as $review) {
        $total_rating += $review['rating'];
    }
    $avg_rating = $total_rating / count($reviews);
}

include 'includes/header.php';
?>

<!-- Product Detail Section -->
<section class="product-detail-section">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">หน้าแรก</a></li>
                <li class="breadcrumb-item"><a href="products.php">ร้านค้า</a></li>
                <li class="breadcrumb-item"><a href="products.php?category=<?php echo $product['category_id']; ?>"><?php echo $product['category_name']; ?></a></li>
                <li class="breadcrumb-item active" aria-current="page"><?php echo $product['name']; ?></li>
            </ol>
        </nav>
        
        <div class="row">
            <!-- Product Images -->
            <div class="col-md-6">
                <div class="product-images">
                    <div class="main-image">
                        <?php if (!empty($product_images)): ?>
                            <img src="<?= htmlspecialchars($product_images[0]['image_path']) ?>"
                                 alt="<?= htmlspecialchars($product['name']) ?>"
                                 id="mainProductImage">
                        <?php else: ?>
                            <img src="assets/images/no-image.jpg" alt="ไม่มีรูปภาพ" id="mainProductImage">
                        <?php endif; ?>
                    </div>
                    <?php if (count($product_images) > 1): ?>
                    <div class="thumbnail-images">
                        <?php foreach ($product_images as $index => $image): ?>
                        <div class="thumbnail <?= $index === 0 ? 'active' : '' ?>"
                             onclick="changeMainImage('<?= htmlspecialchars($image['image_path']) ?>')">
                            <img src="<?= htmlspecialchars($image['image_path']) ?>"
                                 alt="<?= htmlspecialchars($product['name']) ?>">
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Product Info -->
            <div class="col-md-6">
                <div class="product-info">
                    <h1 class="product-title"><?= htmlspecialchars($product['name']) ?></h1>

                    <div class="product-meta">
                        <span class="product-category">หมวดหมู่: <?= htmlspecialchars($product['category_name']) ?></span>
                        <div class="product-rating">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <?php if ($i <= round($avg_rating)): ?>
                                <i class="fas fa-star"></i>
                                <?php else: ?>
                                <i class="far fa-star"></i>
                                <?php endif; ?>
                            <?php endfor; ?>
                            <span>(<?= count($reviews) ?> รีวิว)</span>
                        </div>
                    </div>

                    <div class="product-price">
                        <span class="price">฿<?= number_format($product['price'], 0) ?></span>
                        <?php if (!empty($product['sale_price']) && $product['sale_price'] < $product['price']): ?>
                        <span class="old-price">฿<?= number_format($product['price'], 0) ?></span>
                        <span class="discount">-<?= round(($product['price'] - $product['sale_price']) / $product['price'] * 100) ?>%</span>
                        <?php endif; ?>
                    </div>

                    <div class="product-description">
                        <?= nl2br(htmlspecialchars($product['description'])) ?>
                    </div>

                    <?php if (empty($product['stock']) || $product['stock'] > 0): ?>
                    <div class="product-stock">
                        <span class="in-stock">พร้อมสั่งผลิต</span>
                    </div>
                    <?php else: ?>
                    <div class="product-stock">
                        <span class="out-of-stock">ไม่พร้อมให้บริการ</span>
                    </div>
                    <?php endif; ?>
                    
                    <div class="product-actions">
                        <div class="action-buttons">
                            <a href="shirt-design.php?product_id=<?= $product['id'] ?>" class="btn btn-primary btn-lg">
                                <i class="fas fa-paint-brush"></i> เริ่มออกแบบ
                            </a>
                            <a href="contact.php" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-phone"></i> ติดต่อสั่งซื้อ
                            </a>
                        </div>

                        <div class="contact-info mt-4">
                            <h5>ติดต่อสั่งซื้อ</h5>
                            <div class="contact-methods">
                                <a href="tel:0855599164" class="contact-method">
                                    <i class="fas fa-phone"></i>
                                    <span>************</span>
                                </a>
                                <a href="https://line.me/ti/p/@gtsport" class="contact-method">
                                    <i class="fab fa-line"></i>
                                    <span>@gtsport</span>
                                </a>
                                <a href="https://www.facebook.com/GTSportDesign.1" class="contact-method">
                                    <i class="fab fa-facebook"></i>
                                    <span>GT Sport Design</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Product Tabs -->
        <div class="product-tabs mt-5">
            <ul class="nav nav-tabs" id="productTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab" aria-controls="details" aria-selected="true">รายละเอียด</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button" role="tab" aria-controls="reviews" aria-selected="false">รีวิว (<?php echo count($reviews); ?>)</button>
                </li>
            </ul>
            <div class="tab-content" id="productTabsContent">
                <div class="tab-pane fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
                    <div class="product-details">
                        <?php echo nl2br($product['details']); ?>
                    </div>
                </div>
                <div class="tab-pane fade" id="reviews" role="tabpanel" aria-labelledby="reviews-tab">
                    <div class="product-reviews">
                        <?php if (count($reviews) > 0): ?>
                            <?php foreach ($reviews as $review): ?>
                            <div class="review-item">
                                <div class="review-header">
                                    <div class="reviewer-avatar">
                                        <?= strtoupper(substr($review['customer_name'] ?: 'A', 0, 1)) ?>
                                    </div>
                                    <div class="reviewer-info">
                                        <h5><?= htmlspecialchars($review['customer_name'] ?: 'ลูกค้า') ?></h5>
                                        <div class="review-rating">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <?php if ($i <= $review['rating']): ?>
                                                <i class="fas fa-star"></i>
                                                <?php else: ?>
                                                <i class="far fa-star"></i>
                                                <?php endif; ?>
                                            <?php endfor; ?>
                                        </div>
                                        <span class="review-date"><?= date('d/m/Y', strtotime($review['created_at'])) ?></span>
                                    </div>
                                </div>
                                <div class="review-content">
                                    <p><?= nl2br(htmlspecialchars($review['comment'])) ?></p>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="no-reviews">ยังไม่มีรีวิวสำหรับสินค้านี้</p>
                        <?php endif; ?>
                        
                        <?php if (isset($_SESSION['customer_id'])): ?>
                        <div class="write-review">
                            <h4>เขียนรีวิว</h4>
                            <form action="submit_review.php" method="post">
                                <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                <div class="mb-3">
                                    <label for="rating" class="form-label">คะแนน</label>
                                    <div class="rating-select">
                                        <div class="stars">
                                            <i class="far fa-star" data-rating="1"></i>
                                            <i class="far fa-star" data-rating="2"></i>
                                            <i class="far fa-star" data-rating="3"></i>
                                            <i class="far fa-star" data-rating="4"></i>
                                            <i class="far fa-star" data-rating="5"></i>
                                        </div>
                                        <input type="hidden" name="rating" id="rating" value="0" required>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="comment" class="form-label">ความคิดเห็น</label>
                                    <textarea class="form-control" id="comment" name="comment" rows="3" required></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">ส่ง</button>
                            </form>
                        </div>
                        <?php else: ?>
                        <div class="login-to-review">
                            <p>คุณต้อง <a href="login.php">เข้าสู่ระบบ</a> เพื่อเขียนรีวิว</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Related Products -->
        <?php if (count($related_products) > 0): ?>
        <div class="related-products mt-5">
            <h3 class="section-title">สินค้าที่เกี่ยวข้อง</h3>
            <div class="row">
                <?php foreach ($related_products as $related): ?>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="product-card">
                        <div class="product-image">
                            <img src="<?= htmlspecialchars($related['image_path'] ?: 'assets/images/no-image.jpg') ?>"
                                 alt="<?= htmlspecialchars($related['name']) ?>"
                                 loading="lazy">
                        </div>
                        <div class="product-info">
                            <h3><?= htmlspecialchars($related['name']) ?></h3>
                            <p class="price">฿<?= number_format($related['price'], 0) ?></p>
                            <a href="product-detail.php?id=<?= $related['id'] ?>" class="btn btn-sm btn-primary">ดูรายละเอียด</a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<script>
function changeMainImage(imageSrc) {
    document.getElementById('mainProductImage').src = imageSrc;
    
    // Update active thumbnail
    const thumbnails = document.querySelectorAll('.thumbnail');
    thumbnails.forEach(thumb => {
        if (thumb.querySelector('img').src === imageSrc) {
            thumb.classList.add('active');
        } else {
            thumb.classList.remove('active');
        }
    });
}

function decreaseQuantity() {
    const quantityInput = document.getElementById('quantity');
    if (quantityInput.value > 1) {
        quantityInput.value = parseInt(quantityInput.value) - 1;
    }
}

function increaseQuantity(maxStock) {
    const quantityInput = document.getElementById('quantity');
    if (parseInt(quantityInput.value) < maxStock) {
        quantityInput.value = parseInt(quantityInput.value) + 1;
    }
}

// Star rating functionality
document.addEventListener('DOMContentLoaded', function() {
    const stars = document.querySelectorAll('.stars i');
    const ratingInput = document.getElementById('rating');
    
    stars.forEach(star => {
        star.addEventListener('mouseover', function() {
            const rating = this.getAttribute('data-rating');
            highlightStars(rating);
        });
        
        star.addEventListener('mouseout', function() {
            const currentRating = ratingInput.value;
            highlightStars(currentRating);
        });
        
        star.addEventListener('click', function() {
            const rating = this.getAttribute('data-rating');
            ratingInput.value = rating;
            highlightStars(rating);
        });
    });
    
    function highlightStars(rating) {
        stars.forEach(star => {
            const starRating = star.getAttribute('data-rating');
            if (starRating <= rating) {
                star.classList.remove('far');
                star.classList.add('fas');
            } else {
                star.classList.remove('fas');
                star.classList.add('far');
            }
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>

