// ดึงข้อมูลการตั้งค่า
$stmt = $pdo->query("SELECT * FROM system_settings WHERE setting_key IN ('shipping_cost', 'free_shipping_amount')");
$settings = [];
while ($row = $stmt->fetch()) {
    $settings[$row['setting_key']] = $row['setting_value'];
}

// ค่าจัดส่ง
$shipping_cost = isset($settings['shipping_cost']) ? (float)$settings['shipping_cost'] : 50;
$free_shipping_amount = isset($settings['free_shipping_amount']) ? (float)$settings['free_shipping_amount'] : 1000;

// ตรวจสอบว่าได้รับส่วนลดฟรีค่าจัดส่งหรือไม่
$final_shipping_cost = ($total >= $free_shipping_amount) ? 0 : $shipping_cost;

// คำนวณยอดรวมทั้งหมด
$grand_total = $total + $final_shipping_cost;

include 'includes/header.php';
?>

<!-- Cart Section -->
<section class="cart-section py-5">
    <div class="container">
        <h1 class="page-title">ตะกร้าสินค้า</h1>
        
        <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php 
            echo $_SESSION['success_message'];
            unset($_SESSION['success_message']);
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>
        
        <?php if (empty($_SESSION['cart'])): ?>
        <div class="empty-cart text-center py-5">
            <i class="fas fa-shopping-cart fa-5x mb-3 text-muted"></i>
            <h3>ตะกร้าสินค้าของคุณว่างเปล่า</h3>
            <p>กรุณาเลือกสินค้าที่ต้องการเพิ่มลงในตะกร้า</p>
            <a href="products.php" class="btn btn-primary mt-3">เลือกซื้อสินค้า</a>
        </div>
        <?php else: ?>
        <div class="row">
            <div class="col-lg-8">
                <div class="cart-items">
                    <form action="cart.php" method="post">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>สินค้า</th>
                                    <th>ราคา</th>
                                    <th>จำนวน</th>
                                    <th>รวม</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($_SESSION['cart'] as $index => $item): ?>
                                <tr>
                                    <td>
                                        <div class="cart-product">
                                            <img src="uploads/products/<?php echo $item['image']; ?>" alt="<?php echo $item['name']; ?>">
                                            <div>
                                                <h5><?php echo $item['name']; ?></h5>
                                                <?php if (isset($item['size'])): ?>
                                                <p class="text-muted">ไซส์: <?php echo $item['size']; ?></p>
                                                <?php endif; ?>
                                                <?php if (isset($item['color'])): ?>
                                                <p class="text-muted">สี: <span class="color-dot" style="background-color: <?php echo $item['color']; ?>"></span></p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo number_format($item['price']); ?> บาท</td>
                                    <td>
                                        <div class="quantity-controls">
                                            <button type="button" class="quantity-btn minus" onclick="decreaseQuantity(<?php echo $index; ?>)">-</button>
                                            <input type="number" name="quantity[<?php echo $index; ?>]" value="<?php echo $item['quantity']; ?>" min="1" max="100" id="quantity-<?php echo $index; ?>">
                                            <button type="button" class="quantity-btn plus" onclick="increaseQuantity(<?php echo $index; ?>)">+</button>
                                        </div>
                                    </td>
                                    <td><?php echo number_format($item['price'] * $item['quantity']); ?> บาท</td>
                                    <td>
                                        <a href="cart.php?remove=<?php echo $index; ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('คุณต้องการลบสินค้านี้ออกจากตะกร้าหรือไม่?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        <div class="cart-actions">
                            <button type="submit" name="update_cart" class="btn btn-outline-primary">
                                <i class="fas fa-sync-alt"></i> อัปเดตตะกร้า
                            </button>
                            <a href="products.php" class="btn btn-outline-secondary">
                                <i class="fas fa-shopping-bag"></i> เลือกซื้อสินค้าเพิ่ม
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="cart-summary">
                    <h3>สรุปคำสั่งซื้อ</h3>
                    <div class="summary-item">
                        <span>ยอดรวมสินค้า</span>
                        <span><?php echo number_format($total); ?> บาท</span>
                    </div>
                    <div class="summary-item">
                        <span>ค่าจัดส่ง</span>
                        <span>
                            <?php if ($total >= $free_shipping_amount): ?>
                            <span class="text-success">ฟรี</span>
                            <?php else: ?>
                            <?php echo number_format($shipping_cost); ?> บาท
                            <?php endif; ?>
                        </span>
                    </div>
                    <?php if ($total >= $free_shipping_amount): ?>
                    <div class="summary-item text-success">
                        <small>* ฟรีค่าจัดส่งเมื่อซื้อครบ <?php echo number_format($free_shipping_amount); ?> บาท</small>
                    </div>
                    <?php else: ?>
                    <div class="summary-item">
                        <small>* ซื้อเพิ่มอีก <?php echo number_format($free_shipping_amount - $total); ?> บาท เพื่อรับฟรีค่าจัดส่ง</small>
                    </div>
                    <?php endif; ?>
                    <div class="summary-total">
                        <span>ยอดรวมทั้งสิ้น</span>
                        <span><?php echo number_format($grand_total); ?> บาท</span>
                    </div>
                    <div class="checkout-button">
                        <a href="checkout.php" class="btn btn-primary btn-block">
                            <i class="fas fa-credit-card"></i> ดำเนินการชำระเงิน
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<script>
function decreaseQuantity(index) {
    const quantityInput = document.getElementById(`quantity-${index}`);
    if (parseInt(quantityInput.value) > 1) {
        quantityInput.value = parseInt(quantityInput.value) - 1;
    }
}

function increaseQuantity(index) {
    const quantityInput = document.getElementById(`quantity-${index}`);
    quantityInput.value = parseInt(quantityInput.value) + 1;
}
</script>

<?php include 'includes/footer.php'; ?>
