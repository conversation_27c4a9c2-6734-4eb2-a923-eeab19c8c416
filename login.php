<?php
session_start();
require_once './config/database.php';
$pdo = getDbConnection();

// ถ้าเข้าสู่ระบบแล้วให้ redirect ไปหน้า dashboard
if (isset($_SESSION['customer_logged_in'])) {
    header("Location: customer_dashboard.php");
    exit;
}

$error = '';
$success = '';
$action = $_GET['action'] ?? 'login';

// จัดการการ login
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $_POST['action'] === 'login') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';

    if (!$email || !$password) {
        $error = "กรุณากรอกอีเมลและรหัสผ่าน";
    } else {
        try {
            $stmt = $pdo->prepare("SELECT * FROM customers WHERE email = ? AND is_active = 1");
            $stmt->execute([$email]);
            $customer = $stmt->fetch();

            if ($customer && password_verify($password, $customer['password'])) {
                $_SESSION['customer_logged_in'] = true;
                $_SESSION['customer_id'] = $customer['id'];
                $_SESSION['customer_name'] = $customer['name'];
                $_SESSION['customer_email'] = $customer['email'];

                // อัพเดตเวลาล่าสุดที่เข้าสู่ระบบ
                $stmt = $pdo->prepare("UPDATE customers SET last_login = NOW() WHERE id = ?");
                $stmt->execute([$customer['id']]);

                // ตรวจสอบ redirect parameter
                $redirect = $_GET['redirect'] ?? 'customer_dashboard.php';
                header("Location: " . $redirect);
                exit;
            } else {
                $error = "อีเมลหรือรหัสผ่านไม่ถูกต้อง";
            }
        } catch (Exception $e) {
            $error = "เกิดข้อผิดพลาด: " . $e->getMessage();
        }
    }
}

// จัดการการสมัครสมาชิก
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $_POST['action'] === 'register') {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    if (!$name || !$email || !$phone || !$password) {
        $error = "กรุณากรอกข้อมูลให้ครบถ้วน";
    } elseif ($password !== $confirm_password) {
        $error = "รหัสผ่านไม่ตรงกัน";
    } elseif (strlen($password) < 6) {
        $error = "รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "รูปแบบอีเมลไม่ถูกต้อง";
    } else {
        try {
            // ตรวจสอบว่าอีเมลซ้ำหรือไม่
            $stmt = $pdo->prepare("SELECT id FROM customers WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                $error = "อีเมลนี้ถูกใช้งานแล้ว";
            } else {
                // บันทึกข้อมูลลูกค้าใหม่
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("
                    INSERT INTO customers (name, email, phone, password, is_active, created_at)
                    VALUES (?, ?, ?, ?, 1, NOW())
                ");
                $stmt->execute([$name, $email, $phone, $hashed_password]);

                $customer_id = $pdo->lastInsertId();

                // เข้าสู่ระบบอัตโนมัติ
                $_SESSION['customer_logged_in'] = true;
                $_SESSION['customer_id'] = $customer_id;
                $_SESSION['customer_name'] = $name;
                $_SESSION['customer_email'] = $email;

                header("Location: customer_dashboard.php?welcome=1");
                exit;
            }
        } catch (Exception $e) {
            $error = "เกิดข้อผิดพลาด: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $action === 'register' ? 'สมัครสมาชิก' : 'เข้าสู่ระบบ' ?> - GT Sport Design</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Kanit', sans-serif;
            background: linear-gradient(135deg,rgb(0, 0, 0) 0%,rgb(0, 0, 0) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }

        .login-container {
            max-width: 450px;
            width: 100%;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            backdrop-filter: blur(10px);
        }

        .brand-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .brand-header h1 {
            color:rgb(235, 76, 27);
            font-weight: 700;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .brand-header p {
            color: #6c757d;
            font-size: 1.1rem;
        }

        .form-floating {
            margin-bottom: 20px;
        }

        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            height: auto;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color:rgb(190, 57, 17);
            box-shadow: 0 0 0 0.2rem rgba(190, 66, 17, 0.25);
        }

        .form-floating > label {
            padding: 15px 20px;
            color: #6c757d;
        }

        .btn-login {
            background: linear-gradient(135deg,rgb(243, 95, 10) 0%,rgb(216, 68, 10) 100%);
            border: none;
            border-radius: 15px;
            padding: 15px;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(190, 57, 17, 0.3);
            color: white;
        }

        .tab-buttons {
            display: flex;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 5px;
            margin-bottom: 30px;
        }

        .tab-btn {
            flex: 1;
            padding: 12px;
            text-align: center;
            border-radius: 10px;
            border: none;
            background: transparent;
            color: #6c757d;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .tab-btn.active {
            background:rgb(236, 80, 18);
            color: white;
            box-shadow: 0 2px 10px rgba(224, 74, 4, 0.81);
        }

        .divider {
            display: flex;
            align-items: center;
            margin: 30px 0;
            color: #6c757d;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: #dee2e6;
        }

        .divider span {
            padding: 0 20px;
            font-size: 14px;
        }

        .social-login {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .social-btn {
            flex: 1;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: white;
            color:rgb(125, 112, 108);
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
        }

        .social-btn:hover {
            border-color:rgb(190, 80, 17);
            color:rgb(190, 72, 17);
        }

        .back-to-site {
            text-align: center;
            margin-top: 20px;
        }

        .back-to-site a {
            color: white;
            text-decoration: none;
            font-weight: 500;
        }

        .back-to-site a:hover {
            text-decoration: underline;
        }

        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 20px;
        }

        .password-strength {
            margin-top: 5px;
            font-size: 12px;
        }

        .strength-bar {
            height: 4px;
            border-radius: 2px;
            background: #e9ecef;
            margin-top: 5px;
        }

        .strength-fill {
            height: 100%;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .form-check {
            margin-bottom: 20px;
        }

        .form-check-input:checked {
            background-color: #11be97;
            border-color: #11be97;
        }

        .form-check-input:focus {
            border-color: #11be97;
            box-shadow: 0 0 0 0.25rem rgba(17, 190, 151, 0.25);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- Brand Header -->
            <div class="brand-header">
                <h1><i class="fas fa-tshirt me-2"></i>GT Sport</h1>
                <p>ระบบสมาชิกลูกค้า</p>
            </div>

            <!-- Tab Buttons -->
            <div class="tab-buttons">
                <a href="login.php?action=login" class="tab-btn <?= $action === 'login' ? 'active' : '' ?>">
                    เข้าสู่ระบบ
                </a>
                <a href="login.php?action=register" class="tab-btn <?= $action === 'register' ? 'active' : '' ?>">
                    สมัครสมาชิก
                </a>
            </div>

            <!-- แสดงข้อความแจ้งเตือน -->
            <?php if ($error): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i><?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <!-- Login Form -->
            <?php if ($action === 'login'): ?>
                <form method="POST" id="loginForm">
                    <input type="hidden" name="action" value="login">

                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" name="email"
                               placeholder="อีเมล" value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" required>
                        <label for="email">อีเมล</label>
                    </div>

                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password"
                               placeholder="รหัสผ่าน" required>
                        <label for="password">รหัสผ่าน</label>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="remember" name="remember">
                        <label class="form-check-label" for="remember">
                            จดจำการเข้าสู่ระบบ
                        </label>
                    </div>

                    <button type="submit" class="btn btn-login">
                        <i class="fas fa-sign-in-alt me-2"></i>เข้าสู่ระบบ
                    </button>
                </form>

            <!-- Register Form -->
            <?php else: ?>
                <form method="POST" id="registerForm">
                    <input type="hidden" name="action" value="register">

                    <div class="form-floating">
                        <input type="text" class="form-control" id="name" name="name"
                               placeholder="ชื่อ-นามสกุล" value="<?= htmlspecialchars($_POST['name'] ?? '') ?>" required>
                        <label for="name">ชื่อ-นามสกุล</label>
                    </div>

                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" name="email"
                               placeholder="อีเมล" value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" required>
                        <label for="email">อีเมล</label>
                    </div>

                    <div class="form-floating">
                        <input type="tel" class="form-control" id="phone" name="phone"
                               placeholder="เบอร์โทรศัพท์" value="<?= htmlspecialchars($_POST['phone'] ?? '') ?>" required>
                        <label for="phone">เบอร์โทรศัพท์</label>
                    </div>

                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password"
                               placeholder="รหัสผ่าน" required minlength="6">
                        <label for="password">รหัสผ่าน</label>
                        <div class="password-strength">
                            <div class="strength-bar">
                                <div class="strength-fill"></div>
                            </div>
                            <small class="strength-text">ความปลอดภัยของรหัสผ่าน</small>
                        </div>
                    </div>

                    <div class="form-floating">
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                               placeholder="ยืนยันรหัสผ่าน" required>
                        <label for="confirm_password">ยืนยันรหัสผ่าน</label>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="agree" name="agree" required>
                        <label class="form-check-label" for="agree">
                            ยอมรับ <a href="#" class="text-primary">ข้อตกลงการใช้งาน</a> และ <a href="#" class="text-primary">นโยบายความเป็นส่วนตัว</a>
                        </label>
                    </div>

                    <button type="submit" class="btn btn-login">
                        <i class="fas fa-user-plus me-2"></i>สมัครสมาชิก
                    </button>
                </form>
            <?php endif; ?>

            <!-- Social Login -->
            <div class="divider">
                <span>หรือ</span>
            </div>

            <div class="social-login">
                <a href="#" class="social-btn">
                    <i class="fab fa-google"></i>
                </a>
                <a href="#" class="social-btn">
                    <i class="fab fa-facebook"></i>
                </a>
                <a href="#" class="social-btn">
                    <i class="fab fa-line"></i>
                </a>
            </div>
        </div>

        <!-- Back to site -->
        <div class="back-to-site">
            <a href="index.php">
                <i class="fas fa-arrow-left me-2"></i>กลับหน้าหลัก
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password strength checker
        document.getElementById('password')?.addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.querySelector('.strength-fill');
            const strengthText = document.querySelector('.strength-text');

            if (!strengthBar) return;

            let strength = 0;
            let text = '';
            let color = '';

            if (password.length >= 6) strength += 20;
            if (password.match(/[a-z]/)) strength += 20;
            if (password.match(/[A-Z]/)) strength += 20;
            if (password.match(/[0-9]/)) strength += 20;
            if (password.match(/[^a-zA-Z0-9]/)) strength += 20;

            if (strength < 40) {
                text = 'อ่อน';
                color = '#dc3545';
            } else if (strength < 80) {
                text = 'ปานกลาง';
                color = '#ffc107';
            } else {
                text = 'แข็งแรง';
                color = '#28a745';
            }

            strengthBar.style.width = strength + '%';
            strengthBar.style.backgroundColor = color;
            strengthText.textContent = `ความปลอดภัย: ${text}`;
            strengthText.style.color = color;
        });

        // Password confirmation validation
        document.getElementById('confirm_password')?.addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;

            if (confirmPassword && password !== confirmPassword) {
                this.setCustomValidity('รหัสผ่านไม่ตรงกัน');
            } else {
                this.setCustomValidity('');
            }
        });

        // Phone number validation
        document.getElementById('phone')?.addEventListener('input', function() {
            const phone = this.value.replace(/\D/g, '');
            this.value = phone;

            if (phone.length < 9 || phone.length > 10) {
                this.setCustomValidity('เบอร์โทรศัพท์ต้องมี 9-10 หลัก');
            } else {
                this.setCustomValidity('');
            }
        });

        // Form submission validation
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const submitBtn = this.querySelector('button[type="submit"]');

                if (this.checkValidity()) {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังดำเนินการ...';
                    submitBtn.disabled = true;
                }
            });
        });
    </script>
</body>
</html>
