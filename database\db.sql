-- GT-SportDesign Database Schema
-- Version: 1.0

-- ตารางผู้ดูแลระบบ
CREATE TABLE IF NOT EXISTS `admins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `role` varchar(20) NOT NULL DEFAULT 'admin',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ผู้ดูแลระบบเริ่มต้น (รหัสผ่าน: admin123)
INSERT INTO `admins` (`username`, `password`, `name`, `email`, `role`, `created_at`)
VALUES ('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'ผู้ดูแลระบบ', '<EMAIL>', 'admin', NOW())
ON DUPLICATE KEY UPDATE `id` = `id`;

-- ตารางcategories
CREATE TABLE IF NOT EXISTS `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตารางcustomers
CREATE TABLE IF NOT EXISTS `customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `province` varchar(50) DEFAULT NULL,
  `postal_code` varchar(10) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตารางproducts
CREATE TABLE IF NOT EXISTS `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `sale_price` decimal(10,2) DEFAULT NULL,
  `stock` int(11) DEFAULT NULL,
  `sku` varchar(50) DEFAULT NULL,
  `is_featured` tinyint(1) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตารางproduct_images
CREATE TABLE IF NOT EXISTS `product_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `image_path` varchar(255) NOT NULL,
  `sort_order` int(11) DEFAULT 0,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตารางorders
CREATE TABLE IF NOT EXISTS `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_number` varchar(20) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `customer_name` varchar(100) NOT NULL,
  `customer_email` varchar(100) NOT NULL,
  `customer_phone` varchar(20) DEFAULT NULL,
  `shipping_address` text NOT NULL,
  `shipping_province` varchar(50) NOT NULL,
  `shipping_postal_code` varchar(10) NOT NULL,
  `payment_method` varchar(50) NOT NULL,
  `payment_status` varchar(20) NOT NULL DEFAULT 'pending',
  `payment_slip` varchar(255) DEFAULT NULL,
  `subtotal` decimal(10,2) NOT NULL,
  `shipping_cost` decimal(10,2) NOT NULL,
  `discount` decimal(10,2) DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `cancelled_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_number` (`order_number`),
  KEY `customer_id` (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตารางorder_items
CREATE TABLE IF NOT EXISTS `order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `product_name` varchar(255) NOT NULL,
  `product_price` decimal(10,2) NOT NULL,
  `quantity` int(11) NOT NULL,
  `subtotal` decimal(10,2) NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตารางfile_uploads
CREATE TABLE IF NOT EXISTS `file_uploads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `original_name` varchar(255) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `file_type` varchar(50) NOT NULL,
  `file_size` int(11) NOT NULL,
  `uploaded_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตารางgallery_images
CREATE TABLE IF NOT EXISTS `gallery_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `image_path` varchar(255) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตารางbookings
CREATE TABLE IF NOT EXISTS `bookings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) DEFAULT NULL,
  `customer_name` varchar(100) NOT NULL,
  `customer_email` varchar(100) NOT NULL,
  `customer_phone` varchar(20) NOT NULL,
  `booking_date` date NOT NULL,
  `booking_time` time NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_id` (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตารางdesigns
CREATE TABLE IF NOT EXISTS `designs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image_path` varchar(255) DEFAULT NULL,
  `design_data` longtext DEFAULT NULL,
  `is_template` tinyint(1) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตารางcustomer_designs
CREATE TABLE IF NOT EXISTS `customer_designs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL,
  `design_name` varchar(255) NOT NULL,
  `design_data` longtext NOT NULL,
  `image_path` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_id` (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตารางsystem_settings
CREATE TABLE IF NOT EXISTS `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` varchar(20) NOT NULL DEFAULT 'text',
  `description` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ข้อมูลเริ่มต้น ตั้งค่าระบบ
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `setting_type`, `description`) VALUES
('site_name', 'GT-SportDesign', 'text', 'ชื่อเว็บไซต์'),
('site_description', 'เชี่ยวชาญด้านการออกแบบและเสื้อ', 'text', 'คำอธิบายเว็บไซต์'),
('contact_email', '<EMAIL>', 'text', 'อีเมลติดต่อ'),
('contact_phone', '************', 'text', 'เบอร์โทรศัพท์ติดต่อ'),
('contact_line', '@gtsport', 'text', 'LINE ID'),
('facebook_page', 'https://www.facebook.com/GTSportDesign.1', 'text', 'Facebook Page'),
('min_order_amount', '500', 'number', 'ยอดสั่งซื้อต่ำสุด'),
('shipping_cost', '50', 'number', 'ค่าส่งมาตรฐาน'),
('free_shipping_amount', '1000', 'number', 'ยอดสั่งซื้อต่ำสุดที่ได้ส่งฟรี'),
('order_processing_days', '3-7', 'text', 'ระยะเวลาประมวลผล'),
('promptpay_id', '0855599164', 'text', 'PromptPay ID'),
('primary_color', '#eb4e17', 'text', 'สีหลักของเว็บไซต์'),
('secondary_color', '#ff6b35', 'text', 'สีรองของเว็บไซต์')
ON DUPLICATE KEY UPDATE `setting_key` = `setting_key`;

-- ตารางreviews
CREATE TABLE IF NOT EXISTS `reviews` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  `order_id` int(11) DEFAULT NULL,
  `customer_name` varchar(100) DEFAULT NULL,
  `customer_email` varchar(100) DEFAULT NULL,
  `rating` int(1) NOT NULL DEFAULT 5,
  `comment` text NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'pending',
  `is_featured` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `customer_id` (`customer_id`),
  KEY `product_id` (`product_id`),
  KEY `order_id` (`order_id`),
  KEY `status` (`status`),
  KEY `is_featured` (`is_featured`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ข้อมูลเริ่มต้น reviews
INSERT INTO `reviews` (`customer_name`, `customer_email`, `rating`, `comment`, `status`, `is_featured`) VALUES
('สมชาย ใจดี', '<EMAIL>', 5, 'ใส่สบาย ลายสวย คุณภาพดีมาก แนะนำเลยครับ', 'approved', 1),
('zeza', '<EMAIL>', 5, 'เสื้อคุณภาพดีมาก ผ้านุ่ม ใส่สบาย', 'approved', 1),
('baet', '<EMAIL>', 5, 'ทีมงานบริการดีเยี่ยม ส่งไว คุณภาพดี', 'approved', 1),
('นางสาว สุดา', '<EMAIL>', 4, 'ชุดสวย คุณภาพดี ราคาเหมาะสม', 'approved', 0),
('นาย วิชัย', '<EMAIL>', 5, 'ประทับใจมาก ระบายอากาศดี เหงื่อแห้งเร็ว', 'approved', 1),
('คุณมาลี', '<EMAIL>', 5, 'บริการดีเยี่ยม ส่งตรงเวลา คุณภาพเกินคาด', 'approved', 0)
ON DUPLICATE KEY UPDATE `id` = `id`;

-- ตารางpickup_locations
CREATE TABLE IF NOT EXISTS `pickup_locations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `address` text NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `operating_hours` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ข้อมูลเริ่มต้น ร้านค้า
INSERT INTO `pickup_locations` (`name`, `address`, `phone`, `operating_hours`, `is_active`, `sort_order`) VALUES
('ร้าน GT-SportDesign สาขา 1', '339/7 ม.4 ต.บ้าน จ.ราย', '************', 'ทร์-เสาร์ 9:00-18:00', 1, 1),
('เซ็นต์ ลารี', 'ย์การค้าเซ็นต์ ลารี 1 หน้าร้าน Starbucks', '************', '10:00-21:00', 1, 2)
ON DUPLICATE KEY UPDATE `id` = `id`;

-- ข้อมูลเริ่มต้น categories
INSERT INTO `categories` (`name`, `slug`, `description`, `sort_order`, `is_active`, `created_at`) VALUES
('เสื้อบริษัท', 'company-shirts', 'เสื้อบริษัท สะท้อนความเป็นมืออาชีพ', 1, 1, NOW()),
('เสื้อกิจกรรม', 'activity-shirts', 'เสื้อสำหรับกิจกรรมต่างๆ', 2, 1, NOW()),
('เสื้อกีฬาสี โรงเรียน', 'school-sports', 'เสื้อกีฬาสีสำหรับโรงเรียน', 3, 1, NOW()),
('เสื้อกีฬา', 'sports-shirts', 'เสื้อกีฬาทั่วไป', 4, 1, NOW()),
('เสื้อหน่วยงานราชการ', 'government-shirts', 'เสื้อสำหรับหน่วยงานราชการ', 5, 1, NOW()),
('เสื้อดีไซน์พิเศษ', 'special-design', 'เสื้อออกแบบพิเศษเฉพาะ', 6, 1, NOW())
ON DUPLICATE KEY UPDATE `id` = `id`;

-- ข้อมูลเริ่มต้น products
INSERT INTO `products` (`category_id`, `name`, `slug`, `description`, `price`, `is_featured`, `is_active`, `created_at`) VALUES
(1, 'เสื้อบริษัท', 'company-shirt', 'เสื้อบริษัท สะท้อนความเป็นมืออาชีพ นำเสนอสไตล์ของคุณ พร้อมให้คุณดูดีในทุกวัน', 350.00, 1, 1, NOW()),
(2, 'เสื้อกิจกรรม', 'activity-shirt', 'พร้อมสำหรับทุกความสนุกด้วยเสื้อกิจกรรม ที่ให้คุณดูโดดเด่นและรู้สึกสบายทุกการเคลื่อนไหว', 320.00, 1, 1, NOW()),
(3, 'เสื้อกีฬาสี โรงเรียน', 'school-sport-shirt', 'โชว์พลังทีมด้วยเสื้อกีฬาสีที่สร้างสรรค์เพื่อความสนุกและความสบายในการแข่งขัน', 280.00, 1, 1, NOW()),
(4, 'เสื้อกีฬา', 'sport-shirt', 'เสื้อกีฬาที่ทำให้คุณรู้สึกสบาย มั่นใจในทุกการเคลื่อนไหว', 300.00, 1, 1, NOW()),
(5, 'เสื้อหน่วยงานราชการ', 'government-shirt', 'เสื้อหน่วยงานราชการ บริการไว คุณภาพสูง ประทับใจทุกหน่วยงาน', 380.00, 1, 1, NOW()),
(6, 'เสื้อดีไซน์พิเศษ', 'special-design-shirt', 'ออกแบบพิเศษเฉพาะคุณ กับดีไซน์ที่ไม่ซ้ำใคร', 450.00, 1, 1, NOW())
ON DUPLICATE KEY UPDATE `id` = `id`;

-- ข้อมูลเริ่มต้น product_images
INSERT INTO `product_images` (`product_id`, `image_path`, `sort_order`, `created_at`) VALUES
(1, 'uploads/products/475382494_122220623120074738_924515879995798050_n.jpg', 1, NOW()),
(2, 'uploads/products/475670051_122220623066074738_2795502938249037331_n.jpg', 1, NOW()),
(3, 'uploads/products/475697812_122220623084074738_101296545669764346_n.jpg', 1, NOW()),
(4, 'uploads/products/475755576_122220623090074738_8040878751312398557_n.jpg', 1, NOW()),
(5, 'uploads/products/475815335_122220623102074738_309201310605034286_n.jpg', 1, NOW()),
(6, 'uploads/products/475925098_122220623750074738_1631174588409703078_n.jpg', 1, NOW())
ON DUPLICATE KEY UPDATE `id` = `id`;

-- ข้อมูลเริ่มต้น gallery_images
INSERT INTO `gallery_images` (`title`, `description`, `image_path`, `is_active`, `sort_order`, `created_at`) VALUES
('ผลงานการออกแบบ 1', 'เสื้อกีฬาทีมฟุตบอล', 'https://img2.pic.in.th/pic/LINE_ALBUM__241018_1.jpg', 1, 1, NOW()),
('ผลงานการออกแบบ 2', 'เสื้อบริษัทสีน้ำเงิน', 'https://img5.pic.in.th/file/secure-sv1/LINE_ALBUM__241018_2.jpg', 1, 2, NOW()),
('ผลงานการออกแบบ 3', 'เสื้อกิจกรรมสีแดง', 'https://img5.pic.in.th/file/secure-sv1/LINE_ALBUM__241018_3755795ed32b03ea7.jpg', 1, 3, NOW()),
('ผลงานการออกแบบ 4', 'เสื้อกีฬาสีเขียว', 'https://img5.pic.in.th/file/secure-sv1/LINE_ALBUM__241018_5.jpg', 1, 4, NOW()),
('ผลงานการออกแบบ 5', 'เสื้อทีมบาสเกตบอล', 'https://img5.pic.in.th/file/secure-sv1/644002183119016645_n.jpg', 1, 5, NOW()),
('ผลงานการออกแบบ 6', 'เสื้อหน่วยงานราชการ', 'https://img2.pic.in.th/pic/LINE_ALBUM__241018_7.jpg', 1, 6, NOW()),
('ผลงานการออกแบบ 7', 'เสื้อกีฬาสีส้ม', 'https://img2.pic.in.th/pic/LINE_ALBUM__241018_8.jpg', 1, 7, NOW()),
('ผลงานการออกแบบ 8', 'เสื้อดีไซน์พิเศษ', 'https://img2.pic.in.th/pic/LINE_ALBUM__241018_10.jpg', 1, 8, NOW())
ON DUPLICATE KEY UPDATE `id` = `id`;

-- ตารางcontact_messages
CREATE TABLE IF NOT EXISTS `contact_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `subject` varchar(200) DEFAULT NULL,
  `message` text NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'new',
  `replied_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
